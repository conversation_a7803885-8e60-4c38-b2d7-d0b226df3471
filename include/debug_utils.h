#ifndef DEBUG_UTILS_H
#define DEBUG_UTILS_H

#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>

#ifdef _WIN32
#include <windows.h>
#endif

#ifdef __cplusplus
extern "C" {
#endif

// Windows環境向けの文字コード変換関数
#ifdef _WIN32
void debug_print_utf8(const char* fmt, ...);
#define DEBUG_LOG(fmt, ...) debug_print_utf8("[DEBUG] " fmt "\n", ##__VA_ARGS__)
#else
// 非Windows環境では直接fprintf使用
#define DEBUG_LOG(fmt, ...) fprintf(stderr, "[DEBUG] " fmt "\n", ##__VA_ARGS__); fflush(stderr)
#endif

#ifdef __cplusplus
}
#endif

#endif // DEBUG_UTILS_H
