#ifndef ALSA_PROCESSOR_H
#define ALSA_PROCESSOR_H

#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// ALSA処理の初期化・終了
bool initializeAlsaProcessor(void);
void terminateAlsaProcessor(void);

// ALSA処理の状態取得
bool isAlsaProcessorInitialized(void);

// MIDIイベント処理関数（omnimidi_processorと同じインターフェース）
void processAlsaNoteOn(uint8_t channel, uint8_t note, uint8_t velocity, int trackNumber);
void processAlsaNoteOff(uint8_t channel, uint8_t note, int trackNumber);
void processAlsaControlChange(uint8_t channel, uint8_t control, uint8_t value);

#ifdef __cplusplus
}
#endif

#endif // ALSA_PROCESSOR_H
