#ifndef OMNIMIDI_PROCESSOR_H
#define OMNIMIDI_PROCESSOR_H

#include <stdint.h>
#include <stdbool.h>

// OmniMIDI処理の初期化
bool initializeOmniMidiProcessor(void);

// OmniMIDI処理の終了処理
void terminateOmniMidiProcessor(void);

// MIDIノートONイベントでOmniMIDI音声出力
void processOmniMidiNoteOn(uint8_t channel, uint8_t note, uint8_t velocity, int trackNumber);

// MIDIノートOFFイベントでOmniMIDI音声出力
void processOmniMidiNoteOff(uint8_t channel, uint8_t note, int trackNumber);

// MIDIコントロールチェンジイベントでOmniMIDI送信
void processOmniMidiControlChange(uint8_t channel, uint8_t control, uint8_t value);

// OmniMIDI処理の状態取得
bool isOmniMidiProcessorInitialized(void);

#endif // OMNIMIDI_PROCESSOR_H
