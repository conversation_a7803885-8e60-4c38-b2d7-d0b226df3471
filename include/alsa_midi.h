#ifndef ALSA_MIDI_H
#define ALSA_MIDI_H

#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// ALSA MIDI初期化・終了処理
bool initializeAlsaMidi(void);
void terminateAlsaMidi(void);

// ALSA MIDI状態確認
bool isAlsaMidiInitialized(void);

// MIDI メッセージ送信関数
void alsaSendDirectData(uint32_t msg);
void alsaSendLongData(uint8_t* data, uint32_t size);

// ALSA MIDI リセット
void resetAlsaMidi(void);

#ifdef __cplusplus
}
#endif

#endif // ALSA_MIDI_H
