#ifndef MIDI_PROCESSOR_H
#define MIDI_PROCESSOR_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// 統合MIDI処理の初期化
bool initializeMidiProcessor(void);

// 統合MIDI処理の終了処理
void terminateMidiProcessor(void);

// MIDIノートONイベントの統合処理
void processMidiNoteOn(uint8_t channel, uint8_t note, uint8_t velocity, int trackNumber);

// MIDIノートOFFイベントの統合処理
void processMidiNoteOff(uint8_t channel, uint8_t note, int trackNumber);

// MIDIコントロールチェンジイベントの統合処理
void processMidiControlChange(uint8_t channel, uint8_t control, uint8_t value);

// 統合MIDI処理の状態取得
bool isMidiProcessorInitialized(void);

#ifdef __cplusplus
}
#endif

#endif // MIDI_PROCESSOR_H
