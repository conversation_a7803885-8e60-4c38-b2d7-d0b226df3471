// Windows API includes
#ifdef _WIN32
    #define WIN32_LEAN_AND_MEAN
    #define NOMINMAX
    #include <windows.h>
#endif
#include "omnimidi_processor.h"
#include "OmniMIDI.h"
#include "midi_utils.h"
#include <pthread.h>

// OmniMIDI処理専用の初期化フラグ
static bool omniMidiProcessorInitialized = false;

// OmniMIDI処理の初期化
bool initializeOmniMidiProcessor(void) {
    if (omniMidiProcessorInitialized) {
        return true;
    }

    // OmniMIDIライブラリの初期化は OmniMIDI.c で行われるため、
    // ここでは初期化フラグのみ設定
    omniMidiProcessorInitialized = true;
    return true;
}

// OmniMIDI処理の終了処理
void terminateOmniMidiProcessor(void) {
    omniMidiProcessorInitialized = false;
}



// MIDIノートONイベントでOmniMIDI音声出力
void processOmniMidiNoteOn(uint8_t channel, uint8_t note, uint8_t velocity, int trackNumber) {
    (void)trackNumber; // 未使用パラメータの警告を抑制

    if (!omniMidiProcessorInitialized) {
        return;
    }

    // OmniMIDIでノートON送信
    if (KDMSendDirectData) {
        uint32_t msg = create_midi_message(0x90, channel, note, velocity);
        KDMSendDirectData(msg);
    }
}

// MIDIノートOFFイベントでOmniMIDI音声出力
void processOmniMidiNoteOff(uint8_t channel, uint8_t note, int trackNumber) {
    (void)trackNumber; // 未使用パラメータの警告を抑制

    if (!omniMidiProcessorInitialized) {
        return;
    }

    // OmniMIDIでノートOFF送信
    if (KDMSendDirectData) {
        uint32_t msg = create_midi_message(0x80, channel, note, 0);
        KDMSendDirectData(msg);
    }
}

// MIDIコントロールチェンジイベントでOmniMIDI送信
void processOmniMidiControlChange(uint8_t channel, uint8_t control, uint8_t value) {
    if (!omniMidiProcessorInitialized) {
        return;
    }

    // OmniMIDIでコントロールチェンジ送信
    if (KDMSendDirectData) {
        uint32_t msg = create_midi_message(0xB0, channel, control, value);
        KDMSendDirectData(msg);
    }
}

// OmniMIDI処理の状態取得
bool isOmniMidiProcessorInitialized(void) {
    return omniMidiProcessorInitialized;
}
