// Windows API includes
#ifdef _WIN32
    #define WIN32_LEAN_AND_MEAN
    #define NOMINMAX
    #include <windows.h>
#endif

// 既存のインクルード
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <math.h>
#include <pthread.h>
#include <errno.h>

// 内部実装マーカーを追加してmidiplayer/utils.hの関数にアクセスできるようにする
#define INTERNAL_MIDIPLAYER_IMPLEMENTATION
#include "midiplayer.h"
#include "midiplayer/utils.h"  // 明示的にutils.hをインクルードする
#include "thread_args.h"
#include "midi_utils.h"
#include "OmniMIDI.h"
#include "midi_processor.h"
// Removed visualization includes

// 再生開始前の余白時間（ナノ秒単位）- 1秒
long long INITIAL_SILENCE_NS = 2500000000LL;

// gMidiFile変数をここで定義し、外部からアクセスできるようにする
MIDIFile *gMidiFile = NULL;

// デバッグログを出力するかどうかのフラグ
#define DEBUG_MIDI_PLAYER 0

// デバッグ出力用マクロ
#if DEBUG_MIDI_PLAYER
#define DEBUG_PRINT(...) // fprintf(stderr, __VA_ARGS__)
#else
#define DEBUG_PRINT(...)
#endif

#define MAX(a, b) ((a) > (b) ? (a) : (b))

// GUI functionality removed

// 各トラックの初期状態を保存する構造体の配列
static struct {
    uint8_t **initialHeads;   // ポインタの配列を正しく宣言
    int trackCount;          // 保存しているトラック数
    bool initialized;        // 初期化済みかどうか
} trackInitialState = {
    .initialHeads = NULL,    // 名前を変更（複数形に）
    .trackCount = 0,
    .initialized = false
};

// より確実なトラックの初期状態を保存する関数
void saveTrackInitialState(MIDIFile *midiFile) {
    // 既に初期化済みなら何もしない
    if (trackInitialState.initialized) {
        //fprintf(stderr, "Initial state already saved, skipping\n");
        return;
    }

    //fprintf(stderr, "Saving initial track state...\n");

    // メモリを確保
    trackInitialState.initialHeads = new uint8_t*[midiFile->TrackCount];
    if (!trackInitialState.initialHeads) {
        //fprintf(stderr, "Failed to allocate memory for initial track state\n");
        return;
    }

    // 各トラックの初期ヘッドポインタを保存
    for (int i = 0; i < midiFile->TrackCount; i++) {
        trackInitialState.initialHeads[i] = midiFile->Tracks[i].head;
        //fprintf(stderr, "Saved track %d initial head: %p\n", i, (void*)trackInitialState.initialHeads[i]);
    }

    trackInitialState.trackCount = midiFile->TrackCount;
    trackInitialState.initialized = true;

    //fprintf(stderr, "Successfully saved initial state for %d tracks\n", trackInitialState.trackCount);
}

// グローバル変数に変更して、再生再開時のテンポリセットを防ぐ
static long long last_saved_tempo = 500000; // 初期値は標準テンポ
// テンポ変更の即時反映フラグを追加
static bool tempo_changed = false;

// 完全に書き直されたシーク関数
void playerSeekToPosition(MIDIFile *midiFile, uint64_t targetTick) {
    if (!midiFile || !midiFile->Running) {
        //fprintf(stderr, "Cannot seek: MIDI file not loaded or not running\n");
        return;
    }

    // デバッグ出力を条件付きにする
    #if DEBUG_MIDI_PLAYER
    //fprintf(stderr, "======= SEEKING TO TICK %llu =======\n", (unsigned long long)targetTick);
    #endif

    if (!trackInitialState.initialized) {
        //fprintf(stderr, "ERROR: Initial track state not saved, seeking will fail\n");
        return;
    }

    // 元のテンポを保存
    long long originalTempo = midiFile->CurrentTempo;
    last_saved_tempo = originalTempo;

    // トラックの状態を初期化（ログは必要最小限に）
    for (int i = 0; i < midiFile->TrackCount; i++) {
        if (i < trackInitialState.trackCount) {
            midiFile->Tracks[i].head = trackInitialState.initialHeads[i];
            midiFile->Tracks[i].Tick = 0;
            midiFile->Tracks[i].RunningStatus = 0;
            midiFile->Tracks[i].Ended = false;
        }
    }

    // 現在のティック位置とテンポをリセット
    midiFile->CurrentTick = 0;
    midiFile->CurrentTempo = 500000; // デフォルトテンポ

    // すべてのノートをオフにする（効率化のため一度だけループ）
    for (int ch = 0; ch < 16; ch++) {
        for (int note = 0; note < 128; note++) {
            send_note_off(ch, note, 0);
        }
    }

    // 各トラックの初期デルタタイムを読み込む
    for (int i = 0; i < midiFile->TrackCount; i++) {
        Track *track = &midiFile->Tracks[i];
        if (track->Ended || !track->head) continue;

        // バッファ境界チェック
        if (track->head >= midiFile->Data + midiFile->DataLength) {
            track->Ended = true;
            continue;
        }

        // デルタタイムを読み込む（効率化）
        uint32_t deltaTime = 0;
        uint8_t byte;
        do {
            if (track->head >= midiFile->Data + midiFile->DataLength) {
                track->Ended = true;
                break;
            }
            byte = *track->head++;
            deltaTime = (deltaTime << 7) + (byte & 0x7F);
        } while (byte >= 0x80);

        if (!track->Ended) {
            track->Tick = deltaTime;
        }
    }

    // 進捗表示の間隔を動的に調整
    uint64_t progressInterval = (targetTick > 1000000) ? targetTick / 100 : 10000;

    // 目標位置まで処理
    while (midiFile->CurrentTick < targetTick) {
        uint64_t minTick = UINT64_MAX;
        bool anyTrackActive = false;

        // 次のイベント位置を探す
        for (int i = 0; i < midiFile->TrackCount; i++) {
            Track *track = &midiFile->Tracks[i];
            if (track->Ended) continue;

            anyTrackActive = true;
            if (track->Tick < minTick) {
                minTick = track->Tick;
            }
        }

        // アクティブなトラックがなければループから抜ける
        if (!anyTrackActive) break;

        // 目標位置を超えていたら終了
        if (minTick >= targetTick) break;

        // 現在位置を更新
        midiFile->CurrentTick = minTick;

        // 進捗表示（間引きによる最適化）
        if (midiFile->CurrentTick % progressInterval == 0 ||
            midiFile->CurrentTick + progressInterval >= targetTick) {
            #if DEBUG_MIDI_PLAYER
            //fprintf(stderr, "Seek progress: %llu / %llu (%d%%)\n",
            //        (unsigned long long)midiFile->CurrentTick,
            //        (unsigned long long)targetTick,
            //        (int)((midiFile->CurrentTick * 100) / MAX(1, targetTick)));
            #endif
        }

        // 各トラックのイベントを処理
        for (int i = 0; i < midiFile->TrackCount; i++) {
            Track *track = &midiFile->Tracks[i];
            if (track->Ended || track->Tick != minTick) continue;

            // バッファ境界チェック（一度だけ）
            if (track->head >= midiFile->Data + midiFile->DataLength) {
                track->Ended = true;
                continue;
            }

            // イベントタイプとステータスバイトの処理
            uint8_t eventByte = *track->head;
            if ((eventByte & 0x80) != 0) {
                track->RunningStatus = eventByte;
                track->head++;
            }

            // ステータスチェック
            if (track->RunningStatus == 0) {
                track->Ended = true;
                continue;
            }

            uint8_t status = track->RunningStatus;

            // チャンネルイベント - シークでは実際の音は出さないので効率的に処理
            if (status < 0xF0) {
                // プログラムチェンジイベントは実際に処理する
                if ((status & 0xF0) == 0xC0) {
                    // プログラムチェンジイベント
                    int channel = status & 0x0F;
                    int program = *track->head;

                    // OmniMIDIでプログラムチェンジを実際に送信
                    if (KDMSendDirectData) {
                        uint32_t msg = create_midi_message(0xC0, channel, program, 0);
                        KDMSendDirectData(msg);
                        #if DEBUG_MIDI_PLAYER
                        //fprintf(stderr, "Seek: Applying Program Change ch=%d program=%d\n", channel, program);
                        #endif
                    }

                    // プログラム変更を記録
                    recordProgramChange(channel, program, midiFile->CurrentTick);

                    track->head += 1; // 1バイトデータ
                }
                // その他のチャンネルイベントはスキップ
                else if (status < 0xC0 || status >= 0xE0) {
                    track->head += 2; // 2バイトデータ
                } else {
                    track->head += 1; // 1バイトデータ
                }
            }
            // メタイベント
            else if (status == 0xFF) {
                if (track->head >= midiFile->Data + midiFile->DataLength) {
                    track->Ended = true;
                    continue;
                }

                uint8_t metaType = *track->head++;

                // 長さを読み込む（効率化）
                uint32_t length = 0;
                uint8_t byte;
                do {
                    if (track->head >= midiFile->Data + midiFile->DataLength) {
                        track->Ended = true;
                        break;
                    }
                    byte = *track->head++;
                    length = (length << 7) + (byte & 0x7F);
                } while (byte >= 0x80);

                if (track->Ended) continue;

                // テンポ変更イベントのみ処理（重要なので残す）
                if (metaType == 0x51 && length >= 3 &&
                    track->head + 2 < midiFile->Data + midiFile->DataLength) {
                    uint32_t tempo = (track->head[0] << 16) | (track->head[1] << 8) | track->head[2];
                    midiFile->CurrentTempo = (long long)tempo;

                    // テンポマップに記録（必要な場合のみ）
                    recordTempoChange(midiFile->CurrentTick, tempo);
                }
                // トラック終了
                else if (metaType == 0x2F) {
                    track->Ended = true;
                    continue;
                }

                // データをスキップ
                if (track->head + length <= midiFile->Data + midiFile->DataLength) {
                    track->head += length;
                } else {
                    track->Ended = true;
                    continue;
                }
            }
            // SysExイベント - スキップ
            else if (status == 0xF0 || status == 0xF7) {
                uint32_t length = 0;
                uint8_t byte;

                do {
                    if (track->head >= midiFile->Data + midiFile->DataLength) {
                        track->Ended = true;
                        break;
                    }
                    byte = *track->head++;
                    length = (length << 7) + (byte & 0x7F);
                } while (byte >= 0x80);

                if (track->Ended) continue;

                // データをスキップ
                if (track->head + length <= midiFile->Data + midiFile->DataLength) {
                    track->head += length;
                } else {
                    track->Ended = true;
                    continue;
                }
            }
            // 不明なイベント - 安全にスキップ
            else {
                track->head++;
            }

            // 次のデルタタイムを読み込む（効率化）
            uint32_t deltaTime = 0;
            uint8_t byte;
            do {
                if (track->head >= midiFile->Data + midiFile->DataLength) {
                    track->Ended = true;
                    break;
                }
                byte = *track->head++;
                deltaTime = (deltaTime << 7) + (byte & 0x7F);
            } while (byte >= 0x80);

            if (!track->Ended) {
                track->Tick += deltaTime;
            }
        }
    }

    // 最終位置を設定
    if (midiFile->CurrentTick < targetTick) {
        midiFile->CurrentTick = targetTick;
    }

    // テンポ処理
    if (originalTempo != midiFile->CurrentTempo) {
        last_saved_tempo = midiFile->CurrentTempo;
        tempo_changed = true;
    } else {
        midiFile->CurrentTempo = last_saved_tempo;
    }

    #if DEBUG_MIDI_PLAYER
    //fprintf(stderr, "Seek complete: tick=%llu, tempo=%lld µs/beat (%.1f BPM)\n",
    //        (unsigned long long)midiFile->CurrentTick, midiFile->CurrentTempo,
    //        60000000.0f / (float)midiFile->CurrentTempo);
    //fprintf(stderr, "======= SEEK OPERATION COMPLETED =======\n");
    #endif
}

// シーク状態をクリーンアップする関数
void cleanupTrackInitialState() {
    if (trackInitialState.initialized && trackInitialState.initialHeads) {
        delete[] trackInitialState.initialHeads;
        trackInitialState.initialHeads = NULL;
        trackInitialState.trackCount = 0;
        trackInitialState.initialized = false;
    }

    // テンポマップもクリーンアップ（新規追加）
    extern void cleanupTempoMap();
    cleanupTempoMap();
}

// MIDIメタイベントを処理する関数
void processMidiMetaEvent(uint8_t metaType, uint8_t* data, uint32_t length, uint64_t tick) {
    // 無効なデータをチェック
    if (data == NULL || length == 0) return;

    switch (metaType) {
        case 0x01: // テキストイベント - removed
            // Text event functionality removed
            break;

        case 0x05: // 歌詞イベント - removed
        case 0x06: // マーカーイベント - removed
            // Lyrics and marker functionality removed
            break;
    }
}

void *playMidiFile(void *contents)
{
    struct midiPlayer_play_args *args = (struct midiPlayer_play_args *)contents;
    MIDIFile *midiFile = args->midiFile;

    // グローバル変数に設定
    gMidiFile = midiFile;
    //fprintf(stderr, "playMidiFile: Setting gMidiFile = %p\n", gMidiFile);

    // Visualization functionality removed

    // トラックの初期状態を保存
    saveTrackInitialState(midiFile);

    // プログラム状態の初期化を追加
    initProgramState();

    // 再生開始前の余白を追加
    long long silenceStartTime = get_ns();
    long long silenceEndTime = silenceStartTime + INITIAL_SILENCE_NS;
    while (get_ns() < silenceEndTime && midiFile->Running) {
        // 一時停止中は余白カウントを一時停止
        if (isMidiPaused()) {
            silenceStartTime = get_ns();
            silenceEndTime = silenceStartTime + INITIAL_SILENCE_NS;
            sleepNanos(10000000); // 10ミリ秒待機
            continue;
        }
        sleepNanos(10000000); // 10ミリ秒ごとにチェック
    }

    // テンポマップは既にloader.cで初期化されているため、ここでは再初期化しない
    // initTempoMap(); // 削除または無効化

    // 外部アクセス用関数を呼び出して時間計算用の比率を初期化
    initializeTimeCalculation();  // utils.cで定義した関数を呼び出す

    long long ticker;
    long long target_time = 0;
    long long actual_time = 0;
    long long tempo = 500000; // 初期テンポ（マイクロ秒/四分音符）
    bool was_paused = false;  // 直前に一時停止状態だったかを記録

    // TrackCountが0の場合は処理を中断
    if (midiFile->TrackCount <= 0) {
        //fprintf(stderr, "Error: No valid tracks to play\n");
        return NULL;
    }

    // 初期テンポを確実に設定（後のシーク操作のために重要）
    midiFile->CurrentTempo = tempo;
    last_saved_tempo = tempo; // グローバル変数に初期テンポも保存
    tempo_changed = false;    // 初期化時はテンポ変更なし
    //fprintf(stderr, "Initial tempo set to %lld µs/beat (%.1f BPM)\n",
    //        tempo, 60000000.0f / (float)tempo);

    // ティック当たりのナノ秒数を計算
    long long ns_per_tick = (tempo * 1000) / midiFile->Division;

    // 現在のテンポを保存
    midiFile->CurrentTempo = tempo;

    // 総ティック数を計算 - まずは最大値を求める
    midiFile->TotalTicks = 0;
    for (int i = 0; i < midiFile->TrackCount; i++) {
        // 各トラックの総ティック数を計算し、最大値を総ティック数とする
        Track *track = &(midiFile->Tracks[i]);
        uint8_t *originalHead = track->head;
        uint64_t trackTick = 0;
        bool trackEnded = false;

        // トラックの最後まで読み進めて総ティック数を計算
        while (!trackEnded && track->head < midiFile->Data + midiFile->DataLength) {
            uint32_t deltaTime = 0;
            uint8_t byte;

            // デルタタイムを読み取る
            do {
                byte = *track->head++;
                deltaTime = (deltaTime << 7) + (byte & 0x7F);
            } while (byte >= 0x80 && track->head < midiFile->Data + midiFile->DataLength);

            trackTick += deltaTime;

            // イベントタイプを確認
            uint8_t statusByte;
            if ((*track->head & 0x80) != 0) {
                statusByte = *track->head++;
            } else {
                statusByte = track->RunningStatus;
            }

            // イベントタイプに応じてデータのスキップ
            if (statusByte < 0xF0) {
                // チャンネルメッセージ
                switch (statusByte & 0xF0) {
                    case 0x80: case 0x90: case 0xA0: case 0xB0: case 0xE0:
                        track->head += 2;  // 2バイトデータ
                        break;
                    case 0xC0: case 0xD0:
                        track->head += 1;  // 1バイトデータ
                        break;
                }
            } else if (statusByte == 0xFF) {
                // メタイベント
                uint8_t metaType = *track->head++;

                // メタイベントの長さを読み取る
                uint32_t length = 0;
                do {
                    byte = *track->head++;
                    length = (length << 7) + (byte & 0x7F);
                } while (byte >= 0x80 && track->head < midiFile->Data + midiFile->DataLength);

                // エンドオブトラックチェック
                if (metaType == 0x2F) {
                    trackEnded = true;
                }

                // データをスキップ
                track->head += length;
            } else if (statusByte == 0xF0 || statusByte == 0xF7) {
                // SysExイベント
                uint32_t length = 0;
                do {
                    byte = *track->head++;
                    length = (length << 7) + (byte & 0x7F);
                } while (byte >= 0x80 && track->head < midiFile->Data + midiFile->DataLength);

                // データをスキップ
                track->head += length;
            }

            // ファイル終端に達したらループ終了
            if (track->head >= midiFile->Data + midiFile->DataLength) {
                break;
            }
        }

        // このトラックのティック数が最大なら更新
        if (trackTick > midiFile->TotalTicks) {
            midiFile->TotalTicks = trackTick;
        }

        // ヘッドポインタを元に戻す
        track->head = originalHead;
    }

    for (int c = 0; c < midiFile->TrackCount; c++)
    {
        // ヘッドポインタのバリデーション
        if (midiFile->Tracks[c].head == NULL ||
            midiFile->Tracks[c].head < midiFile->Data ||
            midiFile->Tracks[c].head >= midiFile->Data + midiFile->DataLength) {
            //fprintf(stderr, "Error: Invalid head pointer for track %d\n", c);
            midiFile->Tracks[c].Ended = true;
            continue;
        }

        int nval = 0;
        uint8_t byte1;

        // データの読み込み部分にも境界チェックを追加
        do
        {
            // ポインタの境界チェック
            if (midiFile->Tracks[c].head >= midiFile->Data + midiFile->DataLength) {
                //fprintf(stderr, "Error: Buffer overrun in track %d initialization\n", c);
                midiFile->Tracks[c].Ended = true;
                break;
            }

            byte1 = *(midiFile->Tracks[c].head)++;
            nval = (nval << 7) + (byte1 & 0x7F);
        } while (byte1 >= 0x80 && midiFile->Tracks[c].head < midiFile->Data + midiFile->DataLength);

        // 正常に読めた場合のみTickを更新
        if (!midiFile->Tracks[c].Ended) {
            midiFile->Tracks[c].Tick += nval;
        }
    }

    ticker = get_ns();
    midiFile->LastTime = ticker;
    target_time = ticker;

    int activeTracks = midiFile->TrackCount;

    while (midiFile->Running)
    {
        // 一時停止中は処理を停止し、時間の経過も止める
        if (isMidiPaused()) {
            // 一時停止フラグを設定
            was_paused = true;

            // 一時停止中はtarget_timeを更新しない
            // CPU使用率を下げるために少し待機
            sleepNanos(10000000); // 10ミリ秒待機
            continue;
        }

        // 一時停止から再開したときの処理
        if (was_paused) {
            // 再開時は時間をリセットして、イベントの遅延実行を防止
            ticker = get_ns();
            target_time = ticker;
            midiFile->LastTime = ticker;

            // 保存したテンポを復元
            if (midiFile->CurrentTempo <= 0 || midiFile->CurrentTempo != last_saved_tempo) {
                //fprintf(stderr, "Correcting tempo at resume: %lld -> %lld µs/beat\n",
                //        midiFile->CurrentTempo, last_saved_tempo);
                midiFile->CurrentTempo = last_saved_tempo;
                tempo_changed = true; // 再計算フラグをセット
            }

            // シーク操作後のテンポ適用を確実にするため、明示的にテンポを設定
            tempo = midiFile->CurrentTempo;

            // テンポ情報を出力（デバッグ用）
            //fprintf(stderr, "Resume with tempo: %lld µs/beat (%.1f BPM), tempo_changed=%d\n",
            //        tempo, 60000000.0f / (float)tempo, tempo_changed);

            // 現在のテンポに基づいてns_per_tickを再計算
            ns_per_tick = (tempo * 1000) / midiFile->Division;
            //fprintf(stderr, "Playback resumed: Tempo=%lld µs/beat (%.1f BPM), ns_per_tick=%lld\n",
            //        tempo, 60000000.0f / (float)tempo, ns_per_tick);

            // 再計算フラグを強制的にオンにしてテンポの即時反映を確実に
            tempo_changed = true;

            // Visualization functionality removed - no note state tracking needed

            // 一時停止フラグをリセット
            was_paused = false;
        }

        uint64_t min_tick = UINT64_MAX;
        int activeTrackCount = 0;

        for (int i = 0; i < midiFile->TrackCount; i++)
        {
            Track *track = &(midiFile->Tracks[i]);

            // トラックが既に終了している場合はスキップ
            if (track->Ended) continue;

            // ポインタの有効性チェック - より堅牢に
            if (track->head == NULL || track->head < midiFile->Data ||
                track->head >= midiFile->Data + midiFile->DataLength) {
                //fprintf(stderr, "Warning: Invalid head pointer detected for track %d, marking as ended\n", i);
                track->Ended = true;
                continue;
            }

            activeTrackCount++;

            while (track->Tick <= midiFile->CurrentTick && !track->Ended)
            {
                // 常に境界チェック
                if (track->head >= midiFile->Data + midiFile->DataLength) {
                    //fprintf(stderr, "Error: Buffer overrun detected in track %d\n", i);
                    track->Ended = true;
                    activeTrackCount--;
                    break;
                }

                uint8_t byteValue = *track->head;

                // 以下処理は変更なし（ただし、適宜境界チェックを追加）
                if ((byteValue & 0x80) != 0)
                {
                    track->RunningStatus = byteValue;
                    track->head++;
                }

                if (track->RunningStatus < 0xc0 || (0xe0 <= track->RunningStatus && track->RunningStatus < 0xf0))
                {
                    int statusByte = track->RunningStatus;
                    int dataByte1 = *track->head;
                    int dataByte2 = *(track->head + 1);

                    switch (statusByte & 0xF0)
                    {
                    case 0x80:
                    {
                        int channel = statusByte & 0x0F;
                        int note = dataByte1;
                        // 新しいMIDI処理システムを使用
                        processMidiNoteOff(channel, note, i);
                        // Visualizer functionality removed
                    }
                    break;
                    case 0x90:
                    {
                        int channel = statusByte & 0x0F;
                        int note = dataByte1;
                        int velocity = dataByte2;

                        if (velocity > 0) {
                            // 新しいMIDI処理システムを使用
                            processMidiNoteOn(channel, note, velocity, i);
                            // Visualizer functionality removed
                        } else {
                            // ベロシティ0はノートOFFとして扱う
                            processMidiNoteOff(channel, note, i);
                            // Visualizer functionality removed
                        }
                    }
                    break;
                    case 0xB0: // コントロールチェンジ
                    {
                        int channel = statusByte & 0x0F;
                        int control = dataByte1;
                        int value = dataByte2;
                        // 新しいMIDI処理システムを使用
                        processMidiControlChange(channel, control, value);
                    }
                    break;
                    case 0x65: // ピッチベンドレンジ
                    {
                        int channel = statusByte & 0x0F;
                        int lsb = dataByte1;
                        int msb = dataByte2;
                        // OmniMIDIでピッチベンドレンジ送信
                        if (KDMSendDirectData) {
                            uint32_t msg = create_midi_message(0x65, channel, lsb, msb);
                            KDMSendDirectData(msg);
                        }
                    }
                    break;
                    case 0xE0: // ピッチベンド
                    {
                        int channel = statusByte & 0x0F;
                        int lsb = dataByte1;
                        int msb = dataByte2;
                        // OmniMIDIでピッチベンド送信 (little endian)
                        if (KDMSendDirectData) {
                            uint32_t msg = create_midi_message(0xE0, channel, lsb, msb);
                            KDMSendDirectData(msg);
                        }

                        // ピッチベンド値を計算（7bit+7bit=14bit値: 0-16383）
                        int pitchBendValue = (msb << 7) | lsb;

                        // ピッチベンド機能は無効化されているため、ビジュアライザーには通知しない
                        (void)pitchBendValue; // 未使用変数の警告を抑制
                    }
                    break;
                    case 0xC0: // プログラムチェンジ
                    {
                        int channel = statusByte & 0x0F;
                        int program = dataByte1;
                        // OmniMIDIでプログラムチェンジ送信
                        if (KDMSendDirectData) {
                            uint32_t msg = create_midi_message(0xC0, channel, program, 0);
                            KDMSendDirectData(msg);
                            // プログラム変更を記録
                            recordProgramChange(channel, program, midiFile->CurrentTick);
                        }
                    }
                    break;
                    case 0xD0: // チャンネルアフタータッチ
                    {
                        int channel = statusByte & 0x0F;
                        int pressure = dataByte1;
                        // OmniMIDIでチャンネルアフタータッチ送信
                        if (KDMSendDirectData) {
                            uint32_t msg = create_midi_message(0xD0, channel, pressure, 0);
                            KDMSendDirectData(msg);
                        }
                    }
                    break;
                    }

                    track->head += 2;
                }
                else if (track->RunningStatus < 0xe0)
                {
                    // プログラムチェンジなど1バイトデータのイベント
                    int channel = track->RunningStatus & 0x0F;
                    int program = *track->head;

                    // プログラムチェンジイベントの場合 (0xC0-0xCF)
                    if ((track->RunningStatus & 0xF0) == 0xC0) {
                        // OmniMIDIでプログラムチェンジを送信
                        if (KDMSendDirectData) {
                            uint32_t msg = create_midi_message(0xC0, channel, program, 0);
                            KDMSendDirectData(msg);
                            // プログラム変更を記録
                            recordProgramChange(channel, program, midiFile->CurrentTick);
                        }
                    }

                    track->head += 1;
                }
                else if ((track->RunningStatus & 0xf0) != 0)
                {
                    uint8_t temp2 = 0;
                    if (track->RunningStatus != 0xf0)
                        temp2 = *track->head++;

                    uint32_t lmsglen = 0;
                    uint8_t byte1;

                    do
                    {
                        byte1 = *track->head++;
                        lmsglen = (lmsglen << 7) + (byte1 & 0x7F);
                    } while (byte1 >= 0x80);

                    if ((track->RunningStatus & 0xFF) != 0xF0)
                    {
                        if (temp2 == 0x51)
                        {
                            uint32_t raw_tempo = 0;

                            // バッファ境界チェックを強化
                            if (track->head && track->head + 2 < midiFile->Data + midiFile->DataLength) {
                                raw_tempo = ((uint32_t)track->head[0] << 16) |
                                          ((uint32_t)track->head[1] << 8) |
                                          ((uint32_t)track->head[2]);

                                if (raw_tempo > 0) {
                                    // テンポを更新
                                    tempo = (long long)raw_tempo;
                                    midiFile->CurrentTempo = tempo;
                                    last_saved_tempo = tempo;
                                    tempo_changed = true;

                                    // ノートの時間情報を更新
                                    updateNoteTimings(midiFile->CurrentTick, tempo);

                                    // ns_per_tickを更新
                                    ns_per_tick = (tempo * 1000) / midiFile->Division;

                                    recordTempoChange(midiFile->CurrentTick, tempo);
                                }
                            }
                        }
                        else if (temp2 == 0x2F)
                        {
                            track->Ended = true;
                            activeTracks--;
                        }
                        else if (temp2 == 0x01 || temp2 == 0x05 || temp2 == 0x06) {
                            // ノート情報の前処理を含むメタイベントの処理
                            if (lmsglen > 0 && track->head + lmsglen <= midiFile->Data + midiFile->DataLength) {
                                processMidiMetaEvent(temp2, track->head, lmsglen, midiFile->CurrentTick);

                                // ノート情報の前処理（テキストイベントの場合）
                                if (temp2 == 0x01) {
                                    preprocessNoteInfo(track->head, lmsglen, midiFile->CurrentTick);
                                }
                            }
                        }
                    }

                    track->head += (int)lmsglen;
                }
                // 最後のelse ifにSysExイベント処理を追加（別の「if」にしない）
                else if (track->RunningStatus == 0xF0 || track->RunningStatus == 0xF7) {
                    // F0: 完全なSysEx、F7: 分割または続きのSysEx

                    // SysExイベントのサイズ（可変長）を読み込む
                    uint32_t sysExLength = 0;
                    uint8_t sizeByte;

                    do {
                        if (track->head >= midiFile->Data + midiFile->DataLength) {
                            track->Ended = true;
                            break;
                        }

                        sizeByte = *track->head++;
                        sysExLength = (sysExLength << 7) | (sizeByte & 0x7F);
                    } while (sizeByte & 0x80);

                    if (track->Ended) continue;

                    // SysExメッセージに十分なデータがあるかチェック
                    if (track->head + sysExLength > midiFile->Data + midiFile->DataLength) {
                        //fprintf(stderr, "Warning: SysEx message exceeds file bounds\n");
                        track->Ended = true;
                        continue;
                    }

                    // SysExメッセージを送信（デバッグログ追加）
                    #if DEBUG_MIDI_PLAYER
                    //fprintf(stderr, "SysEx message at tick %llu, length=%u bytes\n",
                    //        (unsigned long long)midiFile->CurrentTick, sysExLength);
                    #endif

                    // SysExメッセージをOmniMIDIに送信
                    if (KDMSendLongData && sysExLength > 0) {
                        // OmniMIDIのKDMSendLongData関数を使ってSysExを送信
                        uint8_t* sysExData = new uint8_t[sysExLength + 1];
                        if (sysExData) {
                            sysExData[0] = track->RunningStatus; // F0またはF7
                            memcpy(sysExData + 1, track->head, sysExLength);

                            // メモリ確保成功のログ
                            #if DEBUG_MIDI_PLAYER
                            //fprintf(stderr, "Sending SysEx: %02X + %02X %02X... (%u bytes)\n",
                            //        track->RunningStatus,
                            //        track->head[0],
                            //        sysExLength > 1 ? track->head[1] : 0,
                            //        sysExLength);
                            #endif

                            // OmniMIDIにSysExメッセージを送信
                            KDMSendLongData(sysExData, sysExLength + 1);
                            delete[] sysExData;
                        }
                    }

                    // 処理後、データポインタを進める
                    track->head += sysExLength;
                }

                // ここから共通処理（インデントを修正して明確化）
                uint32_t nval2 = 0;
                uint8_t byte2;

                do
                {
                    byte2 = (track->head < midiFile->Data + midiFile->DataLength) ? *track->head : 0;
                    if (track->head < midiFile->Data + midiFile->DataLength)
                    {
                        nval2 = (nval2 << 7) + (byte2 & 0x7F);
                        track->head++;
                    }
                } while (byte2 >= 0x80 && track->head < midiFile->Data + midiFile->DataLength);
                track->Tick += nval2;
            }

            if (!track->Ended && track->Tick < min_tick)
                min_tick = track->Tick;
        }

        if (!activeTracks)
        {
            // 全てのトラックが終了した場合
            // midiFile->Runningをfalseにするのではなく、一時停止状態にする
            setMidiPaused(true);  // midi_paused = true の代わりに関数を使用

            // 一時停止中であることをメッセージ出力（デバッグ用、必要に応じてコメントアウト可）
            //fprintf(stderr, "Playback reached end - paused\n");

            // 最終ティック位置を保存（端数を丸めるため）
            midiFile->CurrentTick = midiFile->TotalTicks;

            // ループを抜けてプレイヤーを終了
            break;
        }

        long long deltaTick = min_tick - midiFile->CurrentTick;

        midiFile->CurrentTick += deltaTick;

        // BPM倍率取得前にテンポをチェック
        if (tempo_changed || midiFile->CurrentTempo != last_saved_tempo) {
            //fprintf(stderr, "Tempo update check - tempo_changed=%d, CurrentTempo=%lld, saved_tempo=%lld\n",
            //        tempo_changed, midiFile->CurrentTempo, last_saved_tempo);

            // テンポが変わっていたら修正
            if (midiFile->CurrentTempo != last_saved_tempo) {
                //fprintf(stderr, "Fixing tempo mismatch: %lld -> %lld\n",
                //        midiFile->CurrentTempo, last_saved_tempo);
                midiFile->CurrentTempo = last_saved_tempo;
            }

            // ns_per_tick再計算が必要ならば更新
            // より精密な計算のために桁数の大きい数値で計算
            long long new_ns_per_tick = (midiFile->CurrentTempo * 1000LL) / (long long)midiFile->Division;
            if (new_ns_per_tick != ns_per_tick) {
                //fprintf(stderr, "Recalculating ns_per_tick: %lld -> %lld\n",
                //        ns_per_tick, new_ns_per_tick);
                ns_per_tick = new_ns_per_tick;
            }

            tempo_changed = false; // フラグをリセット
        }

        // 外部から取得するBPM倍率を適用
        double currentBpmMultiplier = (double)getBpmMultiplier();

        // 負の値や不正なテンポの場合に対応
        if (ns_per_tick <= 0) {
            //fprintf(stderr, "Warning: Invalid ns_per_tick detected (%lld), resetting\n", ns_per_tick);
            ns_per_tick = (500000LL * 1000LL) / (long long)midiFile->Division; // デフォルト値で再設定
        }

        // 現在の有効なテンポが最新かチェックして警告
        if (midiFile->CurrentTempo != last_saved_tempo) {
            //fprintf(stderr, "Warning: CurrentTempo (%lld) differs from saved tempo (%lld), correcting\n",
            //        midiFile->CurrentTempo, last_saved_tempo);
            midiFile->CurrentTempo = last_saved_tempo;
        }

        // ns_per_tickが無効な場合は現在のテンポから再計算
        if (ns_per_tick <= 0) {
            //fprintf(stderr, "Warning: Invalid ns_per_tick detected (%lld), recalculating\n", ns_per_tick);
            ns_per_tick = (midiFile->CurrentTempo * 1000) / (long long)midiFile->Division;
        }

        // ティック当たりのナノ秒数にBPM倍率を適用（逆数をかける）
        // 精度を向上させるためにdoubleで計算し、整数に変換する前に四捨五入
        long long adjusted_ns_per_tick = (long long)(((double)ns_per_tick / currentBpmMultiplier) + 0.5);

        // 次のイベントが演奏されるべき理想的な時間を計算（BPM倍率を適用）
        long long tick_time = deltaTick * adjusted_ns_per_tick;
        target_time += tick_time;

        // 現在の時間を取得
        actual_time = get_ns();

        // 理想時間と現在時間の差を計算（これが待つべき時間）
        long long wait_time = target_time - actual_time;

        // Visualizer functionality removed

        // 0より大きい場合のみ待つ（遅れている場合は待たない）
        if (wait_time > 0) {
            sleepNanos(wait_time);
        }

        // テンポ変更があったときはns_per_tickを更新
        // （この部分はテンポ変更の検出部分で行うべき）
        if (tempo != 500000) {
            ns_per_tick = (tempo * 1000) / midiFile->Division;
        }

        // 実際の時間を記録
        midiFile->LastTime = get_ns();
    }

    // 演奏終了時に全てのノートをオフにする
    for (int ch = 0; ch < 16; ch++) {
        for (int note = 0; note < 128; note++) {
            send_note_off(ch, note, 0);
            // Visualizer functionality removed
        }
    }

    // リセットメッセージ送信
    if (KDMReset) {
        KDMReset();
    }

    // Visualization functionality removed

    // クリーンアップ処理
    // cleanupTrackInitialState(); // この行をコメントアウトして、シーク可能な状態を維持

    return NULL;
}
