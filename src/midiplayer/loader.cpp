#include <cstdio>
#include <cstring>
#include <cstdlib>
#include <cstddef> // ptrdiff_tのために追加
#include <pthread.h>
#include <errno.h>
#include <math.h> // fmod関数のために追加

// 内部実装としてマーク
#define INTERNAL_MIDIPLAYER_IMPLEMENTATION
#include "midiplayer.h"
#include "midiplayer/loader.h"
#include "midiplayer/player.h"
#include "midiplayer/utils.h"  // TempoMap構造体の定義のため
#include "thread_args.h"
#include "midi_utils.h"
#include "midi_header.h"

// グローバル変数の定義
MidiLoadingStatus gMidiLoadingStatus = {
    .filename = "",
    .progress = 0.0f,
    .totalTracks = 0,
    .loadedTracks = 0,
    .statusMessage = "Idle",  // Changed from "アイドル状態"
    .isLoading = false,
    .hasError = false,
    .totalTimeMs = 0          // 全体時間を初期化
};

// Windows環境の場合はグローバル変数を定義
#ifdef _WIN32
extern wchar_t gWideFilePath[MAX_PATH];
extern bool gUseWidePath;
#endif

// 進行状況を更新する関数
static void updateLoadingStatus(const char* message, float progress) {
    strncpy(gMidiLoadingStatus.statusMessage, message, 255);
    gMidiLoadingStatus.statusMessage[255] = '\0';
    gMidiLoadingStatus.progress = progress;
}

// 新しい関数: MIDIファイルのテンポイベントを事前スキャン
void scanTempoEventsInMidiFile(MIDIFile *midiFile) {
    if (!midiFile || !midiFile->Data || midiFile->TrackCount <= 0) {
        fprintf(stderr, "Error: Invalid MIDI file for tempo scanning\n");
        return;
    }

    fprintf(stderr, "Scanning MIDI file for tempo events...\n");

    // 各トラックを個別にスキャン
    for (int i = 0; i < midiFile->TrackCount; i++) {
        updateLoadingStatus("Scanning tempo events...", 0.85f + (i * 0.1f / midiFile->TrackCount));

        Track *track = &midiFile->Tracks[i];
        if (track->Ended) continue;

        // 元のヘッドポインタを保存
        uint8_t *originalHead = track->head;
        uint64_t currentTick = 0;
        uint8_t runningStatus = 0;
        bool trackEnded = false;

        // トラックをスキャンしてテンポ変更イベントを探す
        while (!trackEnded && track->head < midiFile->Data + midiFile->DataLength) {
            // デルタタイムを読み取る
            uint32_t deltaTime = 0;
            uint8_t byte;

            do {
                if (track->head >= midiFile->Data + midiFile->DataLength) {
                    trackEnded = true;
                    break;
                }
                byte = *track->head++;
                deltaTime = (deltaTime << 7) | (byte & 0x7F);
            } while (byte & 0x80);

            if (trackEnded) break;

            // 現在のティック位置を更新
            currentTick += deltaTime;

            // ステータスバイト処理
            uint8_t statusByte;
            if (track->head >= midiFile->Data + midiFile->DataLength) {
                trackEnded = true;
                break;
            }

            if (*track->head & 0x80) {
                statusByte = *track->head++;
                runningStatus = statusByte;
            } else {
                statusByte = runningStatus;
            }

            // メタイベント処理
            if (statusByte == 0xFF) {
                if (track->head >= midiFile->Data + midiFile->DataLength) {
                    trackEnded = true;
                    break;
                }

                uint8_t metaType = *track->head++;

                // 可変長バイト長を読み取る
                uint32_t length = 0;
                do {
                    if (track->head >= midiFile->Data + midiFile->DataLength) {
                        trackEnded = true;
                        break;
                    }
                    byte = *track->head++;
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                if (trackEnded) break;

                // テンポ変更イベント (0x51)
                if (metaType == 0x51 && length >= 3 &&
                    track->head + 2 < midiFile->Data + midiFile->DataLength) {
                    // テンポ値を読み取る (24-bit値)
                    uint32_t tempo = ((uint32_t)track->head[0] << 16) |
                                     ((uint32_t)track->head[1] << 8) |
                                     ((uint32_t)track->head[2]);

                    if (tempo > 0) {
                        float bpm = 60000000.0f / (float)tempo;
                        (void)bpm; // 未使用変数の警告を抑制
                        //fprintf(stderr, "Found tempo event at tick %llu: %u (%.2f BPM)\n",
                        //        (unsigned long long)currentTick, tempo, bpm);

                        // テンポマップにテンポ変更を記録
                        addTempoChange(currentTick, tempo);
                    }
                }

                // エンドオブトラックイベント
                else if (metaType == 0x2F) {
                    trackEnded = true;
                    break;
                }

                // メタイベントのデータ部をスキップ
                if (track->head + length <= midiFile->Data + midiFile->DataLength) {
                    track->head += length;
                } else {
                    trackEnded = true;
                    break;
                }
            }
            // SysExイベント
            else if (statusByte == 0xF0 || statusByte == 0xF7) {
                // 長さを読み取る
                uint32_t length = 0;
                do {
                    if (track->head >= midiFile->Data + midiFile->DataLength) {
                        trackEnded = true;
                        break;
                    }
                    byte = *track->head++;
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                if (trackEnded) break;

                // データをスキップ
                if (track->head + length <= midiFile->Data + midiFile->DataLength) {
                    track->head += length;
                } else {
                    trackEnded = true;
                    break;
                }
            }
            // チャンネルメッセージ
            else if (statusByte < 0xF0) {
                int msgType = statusByte & 0xF0;

                // データサイズに基づきスキップ
                if (msgType == 0xC0 || msgType == 0xD0) {
                    track->head += 1; // 1バイトデータ
                } else {
                    track->head += 2; // 2バイトデータ
                }

                if (track->head > midiFile->Data + midiFile->DataLength) {
                    trackEnded = true;
                    break;
                }
            }
        }

        // ヘッドポインタを元の位置に戻す
        track->head = originalHead;
    }

    // テンポマップをソートして、テンポ情報をデバッグ出力
    sortTempoMap();
    debugPrintTempoMap();

    // fprintf(stderr, "Tempo scanning completed: %d tempo changes found\n", tempoMap.count);
}

// 追加: MIDIファイルのノート数をカウントして初期化する関数
void countNotesInMidiFile(MIDIFile *midiFile) {
    if (!midiFile || !midiFile->Data || midiFile->TrackCount <= 0) {
        return;
    }

    fprintf(stderr, "Counting total notes in MIDI file...\n");
    int totalNotes = 0;
    int processedTracks = 0;

    // 各トラックを個別にスキャン
    for (int i = 0; i < midiFile->TrackCount; i++) {
        Track *track = &midiFile->Tracks[i];
        if (track->Ended) continue;

        // 元のヘッドポインタを保存
        uint8_t *originalHead = track->head;
        uint64_t currentTick = 0;
        uint8_t runningStatus = 0;
        bool trackEnded = false;

        // トラックをスキャンしてノートイベントをカウント
        // ファイルサイズが不明な場合は境界チェックを緩和
        while (!trackEnded) {
            // デルタタイムを読み取る
            uint32_t deltaTime = 0;
            uint8_t byte;

            do {
                // より安全な境界チェック
                if (track->head >= midiFile->Data + midiFile->DataLength) {
                    trackEnded = true;
                    break;
                }
                byte = *track->head++;
                deltaTime = (deltaTime << 7) | (byte & 0x7F);
            } while ((byte & 0x80) && !trackEnded);

            if (trackEnded) break;

            // 現在のティック位置を更新
            currentTick += deltaTime;

            // ステータスバイト処理
            uint8_t statusByte;
            if (track->head >= midiFile->Data + midiFile->DataLength) {
                trackEnded = true;
                break;
            }

            if (*track->head & 0x80) {
                statusByte = *track->head++;
                runningStatus = statusByte;
            } else {
                statusByte = runningStatus;
            }

            // ノートONイベント (0x90) でベロシティが0より大きい場合のみカウント
            if ((statusByte & 0xF0) == 0x90) {
                if (track->head + 1 < midiFile->Data + midiFile->DataLength) {
                    uint8_t velocity = *(track->head + 1);
                    if (velocity > 0) {
                        // ノートONのみカウント (ベロシティ > 0)
                        totalNotes++;
                        // 最初の10個のノートをデバッグ出力
                        if (totalNotes <= 10) {
                            fprintf(stderr, "Found note-on #%d in track %d\n", totalNotes, i);
                        }
                    }
                }
            }

            // データスキップ - メッセージタイプによって適切にスキップ
            if (statusByte < 0xF0) {
                // チャンネルメッセージ
                if ((statusByte & 0xF0) == 0xC0 || (statusByte & 0xF0) == 0xD0) {
                    // プログラムチェンジ、チャンネルプレッシャー: 1バイト
                    track->head += 1;
                } else {
                    // その他のチャンネルメッセージ: 2バイト
                    track->head += 2;
                }
            } else if (statusByte == 0xFF) {
                // メタイベント
                if (track->head >= midiFile->Data + midiFile->DataLength) {
                    trackEnded = true;
                    break;
                }
                uint8_t metaType = *track->head++;

                // 長さを読み取る
                uint32_t length = 0;
                do {
                    if (track->head >= midiFile->Data + midiFile->DataLength) {
                        trackEnded = true;
                        break;
                    }
                    byte = *track->head++;
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                if (trackEnded) break;

                // エンドオブトラック
                if (metaType == 0x2F) {
                    trackEnded = true;
                    break;
                }

                // データをスキップ
                if (track->head + length <= midiFile->Data + midiFile->DataLength) {
                    track->head += length;
                } else {
                    trackEnded = true;
                    break;
                }
            } else if (statusByte == 0xF0 || statusByte == 0xF7) {
                // SysExイベント
                uint32_t length = 0;
                do {
                    if (track->head >= midiFile->Data + midiFile->DataLength) {
                        trackEnded = true;
                        break;
                    }
                    byte = *track->head++;
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                if (trackEnded) break;

                // データをスキップ
                if (track->head + length <= midiFile->Data + midiFile->DataLength) {
                    track->head += length;
                } else {
                    trackEnded = true;
                    break;
                }
            }
        }

        // ヘッドポインタを元の位置に戻す
        track->head = originalHead;
        processedTracks++;
    }

    // Visualization functionality removed - total notes tracking removed
    fprintf(stderr, "Processed %d tracks, counted a total of %d note-on events in MIDI file\n",
            processedTracks, totalNotes);
}

// ファイル読み込み時に呼び出される初期化関数
void initializePlaybackState(void) {
    fprintf(stderr, "Initializing playback state...\n");

    // テンポマップを初期化
    initTempoMap();

    // プログラム状態を初期化
    initProgramState();
    fprintf(stderr, "Program state initialized\n");

    // 他の初期化処理...
}

void *loadMidiFile(void *context)
{
    struct midiPlayer_load_args *args = (struct midiPlayer_load_args *)context;
    char *filepath = args->midiFile;
    // --- 修正ここから ---
    char *slash = strrchr(filepath, '/');
    char *backslash = strrchr(filepath, '\\');
    char *filename = NULL;
    if (slash && backslash) {
        filename = (slash > backslash ? slash : backslash) + 1;
    } else if (slash) {
        filename = slash + 1;
    } else if (backslash) {
        filename = backslash + 1;
    } else {
        filename = filepath;
    }
    // --- 修正ここまで ---

    // ロード状態を初期化
    gMidiLoadingStatus.isLoading = true;
    gMidiLoadingStatus.hasError = false;
    gMidiLoadingStatus.progress = 0.0f;
    gMidiLoadingStatus.loadedTracks = 0;
    gMidiLoadingStatus.totalTracks = 0;
    strncpy(gMidiLoadingStatus.filename, filename, 255);
    gMidiLoadingStatus.filename[255] = '\0';
    updateLoadingStatus("Opening MIDI file...", 0.05f);  // Changed from "MIDIファイルを開いています..."

    pthread_t midiPlayer_play_thread;
    int midiPlayer_play_result = 0;

    // ヒープ上にMIDIFile構造体を確保
    MIDIFile *midiFile = new MIDIFile;
    if (!midiFile) {
        fprintf(stderr, "Error: メモリ確保に失敗しました\n");
        updateLoadingStatus("Error: Failed to allocate memory", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        return NULL;
    }

    // 構造体を初期化
    midiFile->TrackCount = 0;
    midiFile->Division = 0;
    midiFile->CurrentTick = 0;
    midiFile->Data = NULL;
    midiFile->DataLength = 0;
    midiFile->Tracks = NULL;
    midiFile->Running = true;

    // より詳細なファイル検証
    FILE *file = NULL;

    #ifdef _WIN32
    if (gUseWidePath) {
        // ワイド文字パスを使ってファイルを開く
        file = _wfopen(gWideFilePath, L"rb");
        if (!file) {
            fprintf(stderr, "Error: could not open file using wide path (error: %d)\n", errno);
            // 通常のパスでフォールバック
            file = fopen(filename, "rb");
        }
    } else {
        // 通常の方法でファイルを開く
        file = fopen(filename, "rb");
    }
    #else
    // 非Windows環境では通常通り
    file = fopen(filename, "rb");
    #endif

    if (!file) {
        fprintf(stderr, "Error: could not open file %s\n", filename);
        updateLoadingStatus("Error: Could not open file", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        delete midiFile;
        return NULL;
    }

    updateLoadingStatus("Parsing MIDI header...", 0.1f);  // Changed

    // ファイルの先頭16バイトをダンプして内容を確認（デバッグ用）
    fprintf(stderr, "File header dump: ");
    unsigned char header_dump[16] = {0};
    if (fread(header_dump, 1, 16, file) == 16) {
        for (int i = 0; i < 16; i++) {
            fprintf(stderr, "%02X ", header_dump[i]);
        }
        fprintf(stderr, "\n");

        // ASCII表示も追加
        fprintf(stderr, "ASCII: ");
        for (int i = 0; i < 16; i++) {
            fprintf(stderr, "%c", (header_dump[i] >= 32 && header_dump[i] < 127) ? header_dump[i] : '.');
        }
        fprintf(stderr, "\n");
    }
    fseek(file, 0, SEEK_SET);  // ファイルポインタを先頭に戻す

    // ファイルサイズを取得する複数の方法を試す
    unsigned long fileSize = 0;
    long ftell_result = 0;

    // 方法1: ftell()を使用
    if (fseek(file, 0, SEEK_END) == 0) {
        ftell_result = ftell(file);
        if (ftell_result > 0) {
            fileSize = (unsigned long)ftell_result;
            fprintf(stderr, "File size obtained via ftell(): %lu bytes\n", fileSize);
        } else {
            fprintf(stderr, "Warning: ftell() returned %ld\n", ftell_result);
        }
    } else {
        fprintf(stderr, "Warning: fseek(SEEK_END) failed\n");
    }
    fseek(file, 0, SEEK_SET);

    // 方法2: ファイルサイズが取得できない場合、実際に読み込んで確認
    if (fileSize == 0) {
        fprintf(stderr, "Attempting to determine file size by reading...\n");

        // 最初の16バイトが読めるかテスト
        unsigned char test_buffer[16];
        size_t test_read = fread(test_buffer, 1, 16, file);
        fseek(file, 0, SEEK_SET);

        if (test_read >= 14) {
            fprintf(stderr, "Successfully read %zu bytes, but file size unknown\n", test_read);
            // 大きなファイルの可能性があるので、動的に拡張する方式を採用
            fileSize = 0; // 不明として扱う
        } else {
            fprintf(stderr, "Error: Could only read %zu bytes from file\n", test_read);
            updateLoadingStatus("Error: File too small", 0.0f);
            gMidiLoadingStatus.hasError = true;
            gMidiLoadingStatus.isLoading = false;
            fclose(file);
            delete midiFile;
            return NULL;
        }
    }

    fprintf(stderr, "Final file size: %lu bytes, MidiHeader size: %zu bytes\n", fileSize, sizeof(MidiHeader));

    // MIDIファイルの最小サイズは14バイト（MThd + 4バイトサイズ + 6バイトヘッダーデータ）
    const unsigned long MIN_MIDI_SIZE = 14;
    if (fileSize > 0 && fileSize < MIN_MIDI_SIZE) {
        fprintf(stderr, "Error: File too small to be a valid MIDI file (got %lu bytes, need at least %lu)\n",
                fileSize, MIN_MIDI_SIZE);
        updateLoadingStatus("Error: File too small", 0.0f);
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        fclose(file);
        delete midiFile;
        return NULL;
    } else if (fileSize == 0) {
        fprintf(stderr, "Info: File size unknown, proceeding with dynamic loading\n");
    }

    // ヘッダー処理部分の改善 - バイト単位で読み込み
    MidiHeader header;
    memset(&header, 0, sizeof(header)); // 初期化

    // ヘッダーを14バイトとして読み込み（構造体パディングを避ける）
    uint8_t headerBytes[14];
    size_t bytesRead = fread(headerBytes, 1, 14, file);
    if (bytesRead < 14) {
        fprintf(stderr, "Error: Failed to read MIDI header (got %zu bytes, expected 14)\n", bytesRead);
        updateLoadingStatus("Error: Failed to read MIDI header", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        fclose(file);
        delete midiFile;
        return NULL;
    }

    // 手動でヘッダーフィールドを設定（ビッグエンディアンからリトルエンディアンに変換）
    memcpy(header.chunkID, headerBytes, 4);
    header.chunkSize = (headerBytes[4] << 24) | (headerBytes[5] << 16) | (headerBytes[6] << 8) | headerBytes[7];
    header.formatType = (headerBytes[8] << 8) | headerBytes[9];
    header.numberOfTracks = (headerBytes[10] << 8) | headerBytes[11];
    header.timeDivision = (headerBytes[12] << 8) | headerBytes[13];

    // デバッグ: 生のバイト値を表示
    fprintf(stderr, "Raw header bytes: ");
    for (int i = 0; i < 14; i++) {
        fprintf(stderr, "%02X ", headerBytes[i]);
    }
    fprintf(stderr, "\n");

    // ヘッダー解析の詳細確認
    fprintf(stderr, "Track count bytes: %02X %02X -> calculated: %u\n",
            headerBytes[10], headerBytes[11], header.numberOfTracks);
    fprintf(stderr, "Time division bytes: %02X %02X -> calculated: %u\n",
            headerBytes[12], headerBytes[13], header.timeDivision);

    // 実際のトラック数が異なる場合の対処
    if (header.numberOfTracks > 10000) {
        fprintf(stderr, "Warning: Header claims %u tracks, but this seems too high\n", header.numberOfTracks);
        fprintf(stderr, "Will process tracks dynamically until end of file\n");
    }

    fprintf(stderr, "-- Header\n");
    fprintf(stderr, "ChunkID: %s\n", header.chunkID);

    // ChunkIDの検証（文字列として有効か確認）
    if (header.chunkID[0] != 'M' || header.chunkID[1] != 'T' ||
        header.chunkID[2] != 'h' || header.chunkID[3] != 'd') {
        fprintf(stderr, "Error: %s is not a valid MIDI file (invalid header signature)\n", filename);
        updateLoadingStatus("Error: Invalid MIDI header signature", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        fclose(file);
        delete midiFile;
        return NULL;
    }

    // 読み込んだヘッダー情報を表示（デバッグ用）
    fprintf(stderr, "Parsed chunk size: %u\n", header.chunkSize);
    fprintf(stderr, "Parsed format type: %u\n", header.formatType);
    fprintf(stderr, "Parsed track count: %u\n", header.numberOfTracks);
    fprintf(stderr, "Parsed time division: %u\n", header.timeDivision);

    // 手動で読み込んだので、バイトオーダー変換は不要
    // （既にビッグエンディアンからリトルエンディアンに変換済み）

    // 合計トラック数を設定
    gMidiLoadingStatus.totalTracks = header.numberOfTracks;
    updateLoadingStatus("Loading track data...", 0.2f);  // Changed

    fprintf(stderr, "Final header values - chunk size: %u\n", header.chunkSize);
    fprintf(stderr, "Final header values - format type: %u\n", header.formatType);
    fprintf(stderr, "Final header values - track count: %u\n", header.numberOfTracks);
    fprintf(stderr, "Final header values - time division: %u\n", header.timeDivision);

    // チャンクサイズの検証を緩和（標準的なサイズではない場合も許容）
    if (header.chunkSize != 6) {
        fprintf(stderr, "Warning: Non-standard header length: %u (expected 6), continuing anyway\n", header.chunkSize);
    }

    // フォーマットタイプの検証を緩和
    if (header.formatType > 2) {
        fprintf(stderr, "Warning: Unusual format type: %u (expected 0, 1, or 2), attempting to process anyway\n", header.formatType);
    }

    if (header.timeDivision > 0x8000) {
        fprintf(stderr, "Warning: SMTPE time code detected - may not be fully supported\n");
    }

    // トラック数のサニティチェックの強化
    if (header.numberOfTracks <= 0) {
        fprintf(stderr, "Error: No tracks found in the MIDI file\n");
        updateLoadingStatus("Error: No tracks in MIDI file", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        fclose(file);
        delete midiFile;
        return NULL;
    }
    else if (header.numberOfTracks > 10000) {  // 上限値を大幅に引き上げ
        fprintf(stderr, "Warning: File claims to have %d tracks, this is unusually high\n", header.numberOfTracks);
    }

    fprintf(stderr, "FormatType: %d\n", header.formatType);
    fprintf(stderr, "TrackSize: %d\n", header.numberOfTracks);
    fprintf(stderr, "Division: %d\n", header.timeDivision);

    // データサイズの計算方法を改善
    // ファイルサイズから直接計算（ヘッダーサイズは14バイト固定）
    unsigned long dataSize;
    if (fileSize > 14) {
        dataSize = fileSize - 14;
        fprintf(stderr, "- Calculated data size from file size: %lu bytes (%0.2f GB)\n",
                dataSize, dataSize / (1024.0 * 1024.0 * 1024.0));
    } else {
        // ファイルサイズが不明または小さい場合は、大きなファイルを想定して大きな初期サイズを設定
        // 9GBファイルの可能性があるので、メモリが許す限り大きな初期サイズを設定
        dataSize = 1024 * 1024 * 1024; // 1GBから開始（大幅に増加）
        fprintf(stderr, "Info: File size unknown, starting with large buffer (%.2f GB)\n",
                dataSize / (1024.0 * 1024.0 * 1024.0));
        fprintf(stderr, "Note: Using large initial buffer to minimize reallocations\n");
    }

    char sizeMsg[256];
    sprintf(sizeMsg, "Data size: %.2f MB - Allocating memory...", dataSize / (1024.0 * 1024.0));  // Changed
    updateLoadingStatus(sizeMsg, 0.25f);

    midiFile->Division = header.timeDivision;
    midiFile->TrackCount = header.numberOfTracks;
    midiFile->CurrentTick = 0;

    // 特に大きなファイルのためのメモリ割り当て改善
    midiFile->Data = new uint8_t[dataSize];
    if (!midiFile->Data) {
        // メモリ割り当て失敗の場合、詳細なエラーメッセージを提供
        fprintf(stderr, "Error: Failed to allocate %lu bytes (%0.2f MB) for MIDI data: %s\n",
                dataSize, dataSize / (1024.0 * 1024.0), strerror(errno));
        updateLoadingStatus("Error: Failed to allocate memory", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        fclose(file);
        delete midiFile;
        return NULL;
    }
    midiFile->DataLength = dataSize;

    // デバッグ: メモリ割り当て情報を表示
    fprintf(stderr, "Allocated MIDI data buffer: base=0x%llx, size=%lu bytes\n",
           (unsigned long long)midiFile->Data, dataSize);

    size_t tracksMemSize = sizeof(Track) * midiFile->TrackCount;
    fprintf(stderr, "- Allocating %zu bytes for %d tracks\n", tracksMemSize, midiFile->TrackCount);

    // トラック数の上限を大幅に緩和（大きなファイル対応）
    if (midiFile->TrackCount > 50000) {
        fprintf(stderr, "Warning: Extremely high track count (%d) detected, limiting to 50000 tracks\n", midiFile->TrackCount);
        midiFile->TrackCount = 50000; // 大きなファイル用の上限
        tracksMemSize = sizeof(Track) * midiFile->TrackCount;
    } else if (midiFile->TrackCount > 10000) {
        fprintf(stderr, "Info: Large track count (%d) detected, this may be a very large MIDI file\n", midiFile->TrackCount);
    }

    midiFile->Tracks = new Track[midiFile->TrackCount];
    if (!midiFile->Tracks) {
        fprintf(stderr, "Error: Failed to allocate memory for MIDI tracks: %s\n", strerror(errno));
        updateLoadingStatus("Error: Failed to allocate track memory", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        delete[] midiFile->Data;
        delete midiFile;
        fclose(file);
        return NULL;
    }

    // トラック配列の初期化を明示的に行う
    memset(midiFile->Tracks, 0, tracksMemSize);

    midiFile->Running = true;

    // ヘッダーの後に移動（14バイト = MThd(4) + size(4) + format(2) + tracks(2) + division(2)）
    long headerEndPos = 14;
    fseek(file, headerEndPos, SEEK_SET);
    fprintf(stderr, "Positioned file pointer at offset %ld (after header)\n", headerEndPos);

    // intからunsigned long longに変更して大きなファイルを扱えるようにする
    unsigned long long offset = 0;

    updateLoadingStatus("Parsing track data...", 0.3f);  // Changed

    // ヘッダー情報から有効なトラック数を記録するための変数を追加
    int validTrackCount = 0;

    for (int i = 0; i < header.numberOfTracks && i < midiFile->TrackCount; i++)
    {
        // トラック毎の進行状況を更新
        float trackProgress = 0.3f + 0.5f * ((float)i / header.numberOfTracks);
        char trackMsg[256];
        sprintf(trackMsg, "Processing track %d/%d...", i+1, header.numberOfTracks);  // Changed
        updateLoadingStatus(trackMsg, trackProgress);
        gMidiLoadingStatus.loadedTracks = i;

        // 最初にトラックを「終了済み」としてマーク、有効な場合のみ変更
        midiFile->Tracks[i].Ended = true;

        MidiTrack track;

        // ファイル位置の妥当性確認
        long currentPos = ftell(file);
        if (currentPos < 0) {
            fprintf(stderr, "Warning: ftell() failed at track %d (errno: %d), stopping process\n", i, errno);
            break;
        }

        // デバッグ: ファイル位置を定期的に表示
        if (i % 100 == 0) {
            fprintf(stderr, "Track %d: file position = %ld\n", i, currentPos);
        }

        // ファイルサイズが既知の場合のみ境界チェック
        if (fileSize > 14) {
            if ((unsigned long)currentPos >= fileSize) {
                fprintf(stderr, "Warning: Reached end of file at track %d, stopping process\n", i);
                break;
            }

            // トラックヘッダーを読み込む前にファイル終端チェック
            if (currentPos + sizeof(MidiTrack) > fileSize) {
                fprintf(stderr, "Warning: Reached end of file at track %d, processing what we have so far\n", i);
                break;
            }
        }

        size_t bytesRead = fread(&track, 1, sizeof(MidiTrack), file);
        if (bytesRead < sizeof(MidiTrack)) {
            fprintf(stderr, "Warning: Could not read complete track header for track %d, got %zu bytes\n", i, bytesRead);
            break;
        }

        // 大きなファイルの場合は100トラックごとにのみ進捗を表示
        if (i % 100 == 0 || i < 10) {
            fprintf(stderr, "Processing track %d/%d\n", i+1, header.numberOfTracks);
        }

        // サイズ制限のチェック - 2GBを超えるファイルへの対応
        // if (offset > 1800000000ULL) { // 約1.8GBでカットオフ（安全マージン）
        //     fprintf(stderr, "Warning: Total data size approaching 2GB limit, stopping at track %d\n", i);
        //     fprintf(stderr, "Current total size: %llu bytes\n", offset);
        //     break;
        // }

        // Track検証コードの改良
        if (track.chunkID[0] != 'M' || track.chunkID[1] != 'T' ||
            track.chunkID[2] != 'r' || track.chunkID[3] != 'k') {
            fprintf(stderr, "Warning: Track %d has invalid chunk ID '%.4s', attempting to recover\n",
                   i, track.chunkID);

            // MThdパターンの検出 - 連結ファイルの可能性
            if (track.chunkID[0] == 'M' && track.chunkID[1] == 'T' &&
                track.chunkID[2] == 'h' && track.chunkID[3] == 'd') {
                fprintf(stderr, "Found another MThd header - this might be a concatenated file\n");
                fprintf(stderr, "Processing tracks read so far as a complete MIDI file\n");
                // 現在のMIDIファイルとして処理を終了し、読み込んだトラックで処理を続行
                break;
            }

            // チャンクIDが無効な場合、次のMTrk検索を試みる
            char buffer[4];
            int found = 0;
            long position = ftell(file) - sizeof(MidiTrack);

            while ((fileSize == 0 || (unsigned long)(position + 4) < fileSize) && !found) {
                fseek(file, position, SEEK_SET);
                if (fread(buffer, 1, 4, file) == 4) {
                    if (buffer[0] == 'M' && buffer[1] == 'T' &&
                        buffer[2] == 'r' && buffer[3] == 'k') {
                        found = 1;
                        fprintf(stderr, "Found MTrk signature at offset %ld\n", position);
                        fseek(file, position, SEEK_SET);
                        bytesRead = fread(&track, 1, sizeof(MidiTrack), file);
                        if (bytesRead != sizeof(MidiTrack)) {
                            fprintf(stderr, "Error: Failed to read track header after recovery\n");
                            break;
                        }
                    } else if (buffer[0] == 'M' && buffer[1] == 'T' &&
                              buffer[2] == 'h' && buffer[3] == 'd') {
                        // 別のMIDIファイルのヘッダーを発見
                        fprintf(stderr, "Found new MThd header at offset %ld - concatenated file detected\n", position);
                        fprintf(stderr, "Processing tracks read so far as a complete MIDI file\n");
                        found = 2; // 特別なフラグ: 現在のファイルの処理を終了
                        break;
                    }
                }
                position++;
            }

            if (found == 2) {
                // 新しいMThdが見つかった場合、現在のファイルの処理を終了
                break;
            }

            if (!found) {
                fprintf(stderr, "Error: Failed to recover from invalid chunk ID\n");
                continue;
            }
        }

        track.chunkSize = swap_uint32(track.chunkSize);

        // トラックサイズの検証を強化・緩和
        if (track.chunkSize <= 0) {
            fprintf(stderr, "Warning: Track %d has zero size, skipping\n", i);
            continue;
        }

        // 大きなトラックサイズも許容
        if (track.chunkSize > 500 * 1024 * 1024) { // 500MB以上のトラックでも処理を試みる
            fprintf(stderr, "Warning: Track %d has very large size: %u bytes (%0.2f MB)\n",
                   i, track.chunkSize, track.chunkSize / (1024.0 * 1024.0));
        }

        // トラックデータ読み込み前のバッファサイズチェックを強化
        if (offset + track.chunkSize > dataSize) {
            fprintf(stderr, "Warning: Track %d data would exceed buffer size, attempting to resize\n", i);

            // 大きなファイル対応：トラックサイズ制限を緩和
            if (track.chunkSize > 500 * 1024 * 1024) { // 500MB以上のトラック
                fprintf(stderr, "Warning: Track %d has extremely large size (%u bytes), skipping\n",
                        i, track.chunkSize);
                continue;
            }

            // バッファサイズを大幅に拡張（再割り当て回数を最小化）
            unsigned long newSize = offset + track.chunkSize + (500 * 1024 * 1024); // 500MB余裕を持って拡張
            fprintf(stderr, "Expanding buffer to %0.2f GB\n", newSize / (1024.0 * 1024.0 * 1024.0));

            uintptr_t oldDataAddr = (uintptr_t)midiFile->Data;
            uint8_t *newData = new uint8_t[newSize];
            if (!newData) {
                fprintf(stderr, "Error: Failed to resize buffer\n");
                fprintf(stderr, "Will process with available data up to track %d\n", i);
                break;
            }
            // 既存のデータをコピー
            memcpy(newData, midiFile->Data, midiFile->DataLength);
            delete[] midiFile->Data;

            // バッファが移動した場合、既存のトラックポインタを更新
            if ((uintptr_t)newData != oldDataAddr) {
                fprintf(stderr, "Buffer moved from 0x%llx to 0x%llx, updating track pointers\n",
                       (unsigned long long)oldDataAddr, (unsigned long long)newData);

                ptrdiff_t offset_diff = (uintptr_t)newData - oldDataAddr;
                for (int j = 0; j < validTrackCount; j++) {
                    if (midiFile->Tracks[j].head != NULL) {
                        midiFile->Tracks[j].head = (uint8_t*)((uintptr_t)midiFile->Tracks[j].head + offset_diff);
                    }
                }
            }

            midiFile->Data = newData;
            midiFile->DataLength = newSize;
            dataSize = newSize;
            fprintf(stderr, "Buffer expanded to %.2f GB\n", newSize / (1024.0 * 1024.0 * 1024.0));
        }

        // 正しいメモリ位置にデータを読み込む
        bytesRead = fread(&midiFile->Data[offset], 1, track.chunkSize, file);

        // データ読み込み後にトラック情報を設定
        midiFile->Tracks[validTrackCount].head = &midiFile->Data[offset];
        midiFile->Tracks[validTrackCount].RunningStatus = 0;
        midiFile->Tracks[validTrackCount].Tick = 0;
        midiFile->Tracks[validTrackCount].Ended = false;

        // デバッグ: ポインタ設定の詳細を表示（最初の10トラックのみ）
        if (validTrackCount < 10) {
            fprintf(stderr, "Track %d: offset=%llu, pointer=0x%llx, data_base=0x%llx, data_length=%d\n",
                   validTrackCount, offset,
                   (unsigned long long)midiFile->Tracks[validTrackCount].head,
                   (unsigned long long)midiFile->Data,
                   midiFile->DataLength);
        }

        if (bytesRead != track.chunkSize)
        {
            fprintf(stderr, "Error: could not read track data, expected %u bytes, got %zu\n",
                   track.chunkSize, bytesRead);
            // 部分的に読み込めた場合でも処理を試みる
            if (bytesRead > 0) {
                fprintf(stderr, "Warning: Will try to process partially read track data\n");
                // ポインタが有効であることを再確認
                fprintf(stderr, "Track %d head pointer: %p, Data base: %p, offset: %llu\n",
                       validTrackCount, (void*)midiFile->Tracks[validTrackCount].head,
                       (void*)midiFile->Data, offset);
                offset += bytesRead;
                validTrackCount++; // 部分的に読み込めたトラックもカウント
            } else {
                break;
            }
        } else {
            offset += bytesRead;
            validTrackCount++; // 有効なトラック数をカウント
            gMidiLoadingStatus.loadedTracks = validTrackCount;

            // 大きなファイルの場合は100トラックごとにのみサイズ情報を表示
            if (i % 100 == 0 || i < 10) {
                fprintf(stderr, "Track %d: %u bytes (Total: %.2f GB)\n",
                       i, (unsigned int)bytesRead, offset / (1024.0 * 1024.0 * 1024.0));
            }
        }

        // 総読み込み量は100トラックごとにのみ表示
        if (i % 100 == 0) {
            fprintf(stderr, "Progress: %d/%d tracks, %.2f GB loaded\n",
                    validTrackCount, header.numberOfTracks, offset / (1024.0 * 1024.0 * 1024.0));
        }
    }

    // 実際の有効なトラック数をTrackCountに設定
    midiFile->TrackCount = validTrackCount;

    // ヘッダーのトラック数と実際のトラック数の差を報告
    if (validTrackCount != header.numberOfTracks) {
        fprintf(stderr, "Info: Header claimed %d tracks, but found %d valid tracks\n",
                header.numberOfTracks, validTrackCount);
        fprintf(stderr, "This is normal for some MIDI file formats\n");
    }

    updateLoadingStatus("Validating track pointers...", 0.85f);  // Changed

    // 実際に読み込んだトラック数を確認
    if (offset == 0 || midiFile->TrackCount == 0) {
        fprintf(stderr, "Error: No valid tracks found in MIDI file\n");
        updateLoadingStatus("Error: No valid tracks found", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        delete[] midiFile->Tracks;
        delete[] midiFile->Data;
        delete midiFile;
        fclose(file);
        return NULL;
    }

    // トラックヘッドポインタの検証部分の修正
    fprintf(stderr, "Validating track pointers...\n");
    int validPointers = 0;
    for (int i = 0; i < validTrackCount; i++) {
        // NULLチェックを追加
        if (midiFile->Tracks[i].head == NULL) {
            fprintf(stderr, "Warning: Track %d has NULL pointer, marking as ended\n", i);
            midiFile->Tracks[i].Ended = true;
            continue;
        }

        // ポインタの範囲チェックを修正
        uintptr_t headPtr = (uintptr_t)midiFile->Tracks[i].head;
        uintptr_t dataStart = (uintptr_t)midiFile->Data;
        uintptr_t dataEnd = dataStart + midiFile->DataLength;

        // デバッグ情報を表示（最初の10個のみ）
        if (i < 10) {
            fprintf(stderr, "Track %d pointer: 0x%llx, range: 0x%llx-0x%llx\n",
                i, (unsigned long long)headPtr, (unsigned long long)dataStart, (unsigned long long)dataEnd);
        }

        if (headPtr >= dataStart && headPtr < dataEnd) {
            validPointers++;
        } else {
            // ポインタが範囲外の場合、トラックを無効としてマーク
            fprintf(stderr, "Warning: Track %d has invalid pointer, marking as ended\n", i);
            midiFile->Tracks[i].Ended = true;
        }
    }
    fprintf(stderr, "Found %d valid track pointers out of %d tracks\n", validPointers, validTrackCount);

    // 有効なポインタが全くない場合は処理を中止
    if (validPointers == 0) {
        fprintf(stderr, "Error: No valid track pointers found\n");
        updateLoadingStatus("Error: No valid track pointers", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        delete[] midiFile->Tracks;
        delete[] midiFile->Data;
        delete midiFile;
        fclose(file);
        return NULL;
    }

    updateLoadingStatus("Preparing playback thread...", 0.9f);  // Changed

    // clearPrerenderedData();  // Prerendering disabled

    // 大きなファイル対応：トラック数制限を大幅に緩和
    unsigned int maxTracks = 50000; // 最大50000トラックまで対応
    if ((unsigned int)midiFile->TrackCount > maxTracks) {
        fprintf(stderr, "Warning: Limiting playback to first %d tracks (out of %d)\n",
                maxTracks, midiFile->TrackCount);
        midiFile->TrackCount = maxTracks;
    } else {
        fprintf(stderr, "Info: Processing %d tracks from large MIDI file\n", midiFile->TrackCount);
    }

    struct midiPlayer_play_args *mp_args = new midiPlayer_play_args;
    mp_args->midiFile = midiFile;  // ヒープ上の構造体へのポインタを渡す

    updateLoadingStatus("Starting playback thread...", 0.95f);  // Changed

    // ファイルロード前にテンポマップを初期化
    initTempoMap();

    // テンポ変更イベントをスキャンしてテンポマップを構築
    scanTempoEventsInMidiFile(midiFile);

    // prerenderMidiFileNotes(midiFile);  // Prerendering disabled

    // 総時間を計算して保存
    gMidiLoadingStatus.totalTimeMs = getMidiTotalTimeMs();

    // スキャン完了後、ノート数をカウント（テンポスキャンの後に追加）
    countNotesInMidiFile(midiFile);

    // BPM情報を出力
    float minBpm = 0.0f, maxBpm = 0.0f, avgBpm = 0.0f;
    getMidiBpmInfo(&minBpm, &maxBpm, &avgBpm);
    fprintf(stderr, "MIDI BPM Info - Min: %.2f, Max: %.2f, Avg: %.2f\n",
            minBpm, maxBpm, avgBpm);

    // 外部アクセス用関数を呼び出して時間計算用の比率を初期化
    initializeTimeCalculation();

    // 読み込み完了後に初期化関数を呼び出す
    initializePlaybackState();

    midiPlayer_play_result = pthread_create(&midiPlayer_play_thread, NULL, playMidiFile, mp_args);

    if (midiPlayer_play_result != 0)
    {
        updateLoadingStatus("Error: Failed to start playback thread", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        delete mp_args;
        delete[] midiFile->Tracks;
        delete[] midiFile->Data;
        delete midiFile;
        fclose(file);
        return NULL;
    }

    // MIDIファイルロード完了
    updateLoadingStatus("MIDI file loading completed", 1.0f);  // Changed

    // 全体時間計算部分を削除
    gMidiLoadingStatus.totalTimeMs = 0;

    // GUI functionality removed - window title update removed

    // ロード完了時にBPM情報を更新
    // updateBpmInfo(); // 削除済み

    // ロード完了シグナルを少し残してから消す
    struct timespec ts;
    ts.tv_sec = 1;  // 1秒間表示
    ts.tv_nsec = 0;
    nanosleep(&ts, NULL);

    gMidiLoadingStatus.isLoading = false;

    pthread_join(midiPlayer_play_thread, NULL);

    // スレッド終了後にクリーンアップ
    delete mp_args;
    delete[] midiFile->Tracks;
    delete[] midiFile->Data;
    delete midiFile;
    fclose(file);

    fprintf(stderr, "MIDI file loaded and played successfully\n");
    return NULL;
}

// 既に player.c で定義されている変数の参照
extern MIDIFile *gMidiFile;
// Add these empty function implementations at the end of the file

// Empty implementation to replace prerendering functionality
void clearPrerenderedData(void) {
    // This function is intentionally empty as prerendering has been removed
    fprintf(stderr, "Note: Prerendering disabled - clearPrerenderedData() does nothing\n");
}

// Empty implementation to replace prerendering functionality
void prerenderMidiFileNotes(MIDIFile *midiFile) {
    // This function is intentionally empty as prerendering has been removed
    // Suppress unused parameter warning
    (void)midiFile;
    fprintf(stderr, "Note: Prerendering disabled - prerenderMidiFileNotes() does nothing\n");
}
