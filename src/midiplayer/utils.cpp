#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <stdbool.h>
#include <math.h>
#include <errno.h> // errno用に追加
#include <float.h> // FLT_MAX定数のために追加（全プラットフォーム共通で必要）

#ifdef _WIN32
#include <windows.h>
// Windows APIを使用する方針に変更し、NT APIは使用しない
#else
#include <time.h>
#include <pthread.h>
#endif

// 内部実装としてマーク
#define INTERNAL_MIDIPLAYER_IMPLEMENTATION
#include "midiplayer.h"
#include "midiplayer/utils.h"
#include "midi_utils.h" // create_midi_message関数のため
#include "thread_args.h"

// 関数のプロトタイプ宣言
static int calculateAccurateTimeFromTick(uint64_t targetTick);

// 一時停止状態を管理するグローバル変数
static bool midi_paused = false;

// BPM変更用の変数（1.0=通常速度）
static float bpmMultiplier = 1.0f;

// BPM倍率に完全に影響されない時間計算のための変数
static long long initialTempo = 500000; // 初期テンポを保存

// ティックベースの時間計算のための変数
static double tickToMsRatio = 0.0;  // 1ティックあたりのミリ秒数

// テンポマップ実装（ヘッダーで定義されたまま）- 初期化構文を修正
TempoMap tempoMap = {};  // C++では空の初期化で十分

// テンポマップが既に初期化されているかどうかを追跡する変数
static bool tempoMapInitialized = false;

// 総時間のキャッシュ用変数
static int cachedTotalTimeMs = -1;

// GM音源の楽器名（プログラム番号に対応）
static const char* gmInstrumentNames[128] = {
    // Piano (0-7)
    "Acoustic Grand Piano", "Bright Acoustic Piano", "Electric Grand Piano", "Honky-tonk Piano",
    "Electric Piano 1", "Electric Piano 2", "Harpsichord", "Clavinet",
    // Chromatic Percussion (8-15)
    "Celesta", "Glockenspiel", "Music Box", "Vibraphone",
    "Marimba", "Xylophone", "Tubular Bells", "Dulcimer",
    // Organ (16-23)
    "Drawbar Organ", "Percussive Organ", "Rock Organ", "Church Organ",
    "Reed Organ", "Accordion", "Harmonica", "Tango Accordion",
    // Guitar (24-31)
    "Acoustic Guitar (nylon)", "Acoustic Guitar (steel)", "Electric Guitar (jazz)", "Electric Guitar (clean)",
    "Electric Guitar (muted)", "Overdriven Guitar", "Distortion Guitar", "Guitar harmonics",
    // Bass (32-39)
    "Acoustic Bass", "Electric Bass (finger)", "Electric Bass (pick)", "Fretless Bass",
    "Slap Bass 1", "Slap Bass 2", "Synth Bass 1", "Synth Bass 2",
    // Strings (40-47)
    "Violin", "Viola", "Cello", "Contrabass",
    "Tremolo Strings", "Pizzicato Strings", "Orchestral Harp", "Timpani",
    // Ensemble (48-55)
    "String Ensemble 1", "String Ensemble 2", "Synth Strings 1", "Synth Strings 2",
    "Choir Aahs", "Voice Oohs", "Synth Voice", "Orchestra Hit",
    // Brass (56-63)
    "Trumpet", "Trombone", "Tuba", "Muted Trumpet",
    "French Horn", "Brass Section", "SynthBrass 1", "SynthBrass 2",
    // Reed (64-71)
    "Soprano Sax", "Alto Sax", "Tenor Sax", "Baritone Sax",
    "Oboe", "English Horn", "Bassoon", "Clarinet",
    // Pipe (72-79)
    "Piccolo", "Flute", "Recorder", "Pan Flute",
    "Blown Bottle", "Shakuhachi", "Whistle", "Ocarina",
    // Synth Lead (80-87)
    "Lead 1 (square)", "Lead 2 (sawtooth)", "Lead 3 (calliope)", "Lead 4 (chiff)",
    "Lead 5 (charang)", "Lead 6 (voice)", "Lead 7 (fifths)", "Lead 8 (bass + lead)",
    // Synth Pad (88-95)
    "Pad 1 (new age)", "Pad 2 (warm)", "Pad 3 (polysynth)", "Pad 4 (choir)",
    "Pad 5 (bowed)", "Pad 6 (metallic)", "Pad 7 (halo)", "Pad 8 (sweep)",
    // Synth Effects (96-103)
    "FX 1 (rain)", "FX 2 (soundtrack)", "FX 3 (crystal)", "FX 4 (atmosphere)",
    "FX 5 (brightness)", "FX 6 (goblins)", "FX 7 (echoes)", "FX 8 (sci-fi)",
    // Ethnic (104-111)
    "Sitar", "Banjo", "Shamisen", "Koto",
    "Kalimba", "Bag pipe", "Fiddle", "Shanai",
    // Percussive (112-119)
    "Tinkle Bell", "Agogo", "Steel Drums", "Woodblock",
    "Taiko Drum", "Melodic Tom", "Synth Drum", "Reverse Cymbal",
    // Sound Effects (120-127)
    "Guitar Fret Noise", "Breath Noise", "Seashore", "Bird Tweet",
    "Telephone Ring", "Helicopter", "Applause", "Gunshot"
};

// グローバルな状態変数
ProgramChangeState programState;

// プログラムチェンジ状態の初期化
void initProgramState(void) {
    memset(&programState, 0, sizeof(ProgramChangeState));
    // デフォルトでは全チャンネルのプログラムは0（アコースティックグランドピアノ）
    for (int i = 0; i < 16; i++) {
        programState.programs[i] = 0;
        programState.hasChanged[i] = false;
        programState.lastChangeTick[i] = 0;
    }
    // チャンネル9（10番目）はパーカッション
    programState.programs[9] = 0;  // GM規格ではチャンネル10はパーカッション
}

// プログラム変更を記録する
void recordProgramChange(uint8_t channel, uint8_t program, uint64_t tick) {
    if (channel >= 16) return; // チャンネル範囲チェック

    // 変更があった場合のみ処理
    if (programState.programs[channel] != program) {
        //fprintf(stderr, "Program change on channel %d: %d -> %d (instrument: %s) at tick %llu\n",
        //        channel + 1, programState.programs[channel], program,
        //        gmInstrumentNames[program], (unsigned long long)tick);
    }

    programState.programs[channel] = program;
    programState.lastChangeTick[channel] = tick;
    programState.hasChanged[channel] = true;
}

// 現在のプログラムを取得する
uint8_t getCurrentProgram(uint8_t channel) {
    if (channel >= 16) return 0;
    return programState.programs[channel];
}

// 相対時間モードの変数を追加
static bool useRelativeTime = false;
static uint64_t relativeStartTick = 0;  // 相対時間の開始ティック
static int relativeStartTimeMs = 0;     // 相対時間の開始時間(ms)

// 相対時間モードを設定する関数
void setRelativeTimeMode(bool enabled) {
    useRelativeTime = enabled;

    // 相対時間モードを有効にするときは現在位置を基準点に設定
    if (enabled && gMidiFile && gMidiFile->Running) {
        relativeStartTick = gMidiFile->CurrentTick;
        relativeStartTimeMs = calculateAccurateTimeFromTick(relativeStartTick);
    } else {
        // 無効にするときは基準点をリセット
        relativeStartTick = 0;
        relativeStartTimeMs = 0;
    }
}

// 相対時間の基準点をリセットする関数
void resetRelativeTimePosition() {
    if (gMidiFile && gMidiFile->Running) {
        relativeStartTick = gMidiFile->CurrentTick;
        relativeStartTimeMs = calculateAccurateTimeFromTick(relativeStartTick);
    }
}

// 相対時間モードの状態を取得する関数
bool isRelativeTimeMode() {
    return useRelativeTime;
}

// 現在の楽器名を取得する
const char* getCurrentInstrumentName(uint8_t channel) {
    if (channel >= 16) return "Unknown";

    // チャンネル10（インデックス9）は常にパーカッション
    if (channel == 9) {
        return "Percussion";
    }

    uint8_t program = programState.programs[channel];
    if (program >= 128) return "Unknown";

    return gmInstrumentNames[program];
}

// プログラム状態のクリーンアップ
void cleanupProgramState(void) {
    // 今後のために拡張性を持たせる
    memset(&programState, 0, sizeof(ProgramChangeState));
}

// テンポマップの初期化関数の実装 - 修正版
void initTempoMap(void) {
    // 配列は既にヘッダーで割り当てられているので、
    // 単に内容を初期化するだけ
    memset(tempoMap.changes, 0, sizeof(tempoMap.changes));

    // 最初のテンポを設定（デフォルトテンポ 120BPM = 500000μs/beat）
    tempoMap.count = 1;
    tempoMap.changes[0].tick = 0;
    tempoMap.changes[0].tempo = 500000;

    // 初期化フラグを設定
    tempoMapInitialized = true;

    // キャッシュをクリア
    cachedTotalTimeMs = -1;

    //fprintf(stderr, "Initialized tempo map with default tempo: 120 BPM\n");
}

// テンポ変更を追加する関数 - 修正版
void addTempoChange(uint64_t tick, long long tempo) {
    // テンポマップが初期化されていなければ初期化
    if (!tempoMapInitialized) {
        initTempoMap();
    }

    // 無効なテンポ値を無視
    if (tempo <= 0) {
        return;
    }

    // 同じティック位置のテンポ変更があれば上書き
    for (int i = 0; i < tempoMap.count; i++) {
        if (tempoMap.changes[i].tick == tick) {
            tempoMap.changes[i].tempo = tempo;
            //fprintf(stderr, "Updated tempo at tick %llu: %lld (%.2f BPM)\n",
            //        (unsigned long long)tick, tempo, 60000000.0 / (double)tempo);
            return;
        }
    }

    // 配列が一杯なら何もしない
    if (tempoMap.count >= 1024) {
        //fprintf(stderr, "Warning: Tempo map is full, cannot add more tempo changes\n");
        return;
    }

    // 新しいテンポ変更を追加（ティック順にソートされるように挿入）
    int insertPos = tempoMap.count;
    for (int i = 0; i < tempoMap.count; i++) {
        if (tick < tempoMap.changes[i].tick) {
            insertPos = i;
            break;
        }
    }

    // 挿入位置以降の要素を後ろにずらす
    for (int i = tempoMap.count; i > insertPos; i--) {
        tempoMap.changes[i] = tempoMap.changes[i-1];
    }

    // 新しいテンポ変更を挿入
    tempoMap.changes[insertPos].tick = tick;
    tempoMap.changes[insertPos].tempo = tempo;
    tempoMap.count++;

    // キャッシュをクリア
    cachedTotalTimeMs = -1;

    //fprintf(stderr, "Added new tempo at tick %llu: %lld (%.2f BPM), now have %d tempo changes\n",
    //        (unsigned long long)tick, tempo, 60000000.0 / (double)tempo, tempoMap.count);
}

// テンポ変更を記録する関数 (再生中状態用)
void recordTempoChange(uint64_t tick, long long tempo) {
    // addTempoChange関数を呼び出すだけ
    addTempoChange(tick, tempo);
}

// テンポマップをクリーンアップする関数 - 修正版
void cleanupTempoMap() {
    // 静的配列なので、単にカウンタをリセットして、
    // 初期化状態を反映するフラグを更新する
    memset(tempoMap.changes, 0, sizeof(tempoMap.changes));
    tempoMap.count = 0;
    tempoMapInitialized = false;

    // 総時間のキャッシュもリセット
    cachedTotalTimeMs = -1;
}

// 指定したナノ秒だけ実行を遅延させる - クロスプラットフォーム対応
void delayExecution(long nanoseconds)
{
    #ifdef _WIN32
        // Windows標準API（Sleep）を使用
        // ナノ秒をミリ秒に変換（切り上げ）
        DWORD ms = (DWORD)((nanoseconds + 999999) / 1000000);
        Sleep(ms);
    #else
        struct timespec ts;
        ts.tv_sec = 0;
        ts.tv_nsec = nanoseconds;
        nanosleep(&ts, NULL);
    #endif
}

// 現在時刻をナノ秒単位で取得 - クロスプラットフォーム対応（最適化版）
long long get_ns()
{
    #ifdef _WIN32
        // QueryPerformanceCounter（Windows標準API）を使用
        static LARGE_INTEGER freq;
        static int init = 0;
        static LARGE_INTEGER start;
        static double freq_reciprocal; // 除算を乗算に変換するための逆数

        if (!init) {
            QueryPerformanceFrequency(&freq);
            QueryPerformanceCounter(&start);
            freq_reciprocal = 1000000000.0 / (double)freq.QuadPart; // 事前計算
            init = 1;
        }

        LARGE_INTEGER now;
        QueryPerformanceCounter(&now);

        // 開始点からの経過時間をナノ秒に変換（乗算で高速化）
        return (long long)((now.QuadPart - start.QuadPart) * freq_reciprocal);
    #else
        // POSIX/Unix clock_gettime
        struct timespec ts;
        clock_gettime(CLOCK_MONOTONIC, &ts);
        return (long long)ts.tv_sec * 1000000000LL + ts.tv_nsec;
    #endif
}

// 指定したナノ秒だけ正確に待機する - クロスプラットフォーム対応（最適化版）
void sleepNanos(long long nanos)
{
    // 非常に短い待機時間の場合は何もしない（オーバーヘッド削減）
    if (nanos <= 1000) { // 1μs以下
        return;
    }

    #ifdef _WIN32
        // 高精度タイマーを使用した待機（最適化版）
        static LARGE_INTEGER freq;
        static int init = 0;
        static double freq_reciprocal;

        if (!init) {
            QueryPerformanceFrequency(&freq);
            freq_reciprocal = 1000000000.0 / (double)freq.QuadPart;
            init = 1;
        }

        LARGE_INTEGER start, now;
        QueryPerformanceCounter(&start);

        // 待機時間（論理クロック単位）
        long long wait_ticks = (long long)(nanos / freq_reciprocal);

        // 適応的待機戦略
        if (nanos > 5000000) { // 5ms以上の場合
            // 大部分をSleepで待機
            Sleep((DWORD)((nanos - 1000000) / 1000000)); // 1ms手前まで
        }

        // 残りの時間をスピンウェイトで正確に待機
        do {
            QueryPerformanceCounter(&now);
        } while ((now.QuadPart - start.QuadPart) < wait_ticks);
    #else
        // POSIX nanosleep implementation（最適化版）
        if (nanos > 5000000) { // 5ms以上の場合
            struct timespec sleepDuration;
            sleepDuration.tv_sec = nanos / 1000000000;
            sleepDuration.tv_nsec = nanos % 1000000000;
            nanosleep(&sleepDuration, NULL);
        } else {
            // 短い待機時間はスピンウェイト
            struct timespec start, now;
            clock_gettime(CLOCK_MONOTONIC, &start);

            do {
                clock_gettime(CLOCK_MONOTONIC, &now);
            } while ((now.tv_sec - start.tv_sec) * 1000000000LL +
                     (now.tv_nsec - start.tv_nsec) < nanos);
        }
    #endif
}

// 再生/一時停止を切り替える
void toggleMidiPlayPause()
{
    midi_paused = !midi_paused;

    if (KDMSendDirectData && midi_paused) {
        // 一時停止時にすべてのコントローラーをリセット (120=All Sound Off, 123=All Notes Off)
        for (int ch = 0; ch < 16; ch++) {
            uint32_t msg1 = create_midi_message(0xB0, ch, 120, 0);
            uint32_t msg2 = create_midi_message(0xB0, ch, 123, 0);
            KDMSendDirectData(msg1);
            KDMSendDirectData(msg2);
        }
    }

    //printf("MIDI playback %s\n", midi_paused ? "paused" : "resumed");
}

// 一時停止状態を取得
bool isMidiPaused()
{
    return midi_paused;
}

// MIDIの進行状況を0.0～1.0の範囲で返す
float getMidiProgress() {
    if (!gMidiFile || !gMidiFile->Running) {
        return 0.0f;
    }

    // 総ティック数またはナノ秒総時間が0の場合は0を返す
    if (gMidiFile->TotalTicks <= 0) {
        return 0.0f;
    }

    // 相対時間モードの場合は相対的な進行状況を計算
    if (useRelativeTime && gMidiFile->TotalTicks > relativeStartTick) {
        uint64_t currentRelativeTick = gMidiFile->CurrentTick > relativeStartTick ?
                                      gMidiFile->CurrentTick - relativeStartTick : 0;
        uint64_t totalRelativeTicks = gMidiFile->TotalTicks - relativeStartTick;
        return (float)currentRelativeTick / (float)totalRelativeTicks;
    }

    // 通常モードでは全体に対する現在位置の割合
    return (float)gMidiFile->CurrentTick / (float)gMidiFile->TotalTicks;
}

// 初期化時にティック→時間変換比率を設定
void initializeTimeCalculation() {
    if (!gMidiFile || !gMidiFile->Running || gMidiFile->Division <= 0) {
        return;
    }

    // 標準テンポ (500000 µs/beat = 120 BPM)を使用して基準比率を計算
    long long tempo = 500000; // 標準テンポを基準にする

    // ティック→ミリ秒の変換率を計算 (tempo / division / 1000)
    tickToMsRatio = (double)tempo / (double)gMidiFile->Division / 1000.0;

    // テンポマップが空ならデフォルトテンポを追加
    if (!tempoMapInitialized || tempoMap.count == 0) {
        initTempoMap();
    }

    //fprintf(stderr, "Initialized tick-to-ms ratio: %.6f ms/tick (base tempo=120BPM, division=%d)\n",
    //        tickToMsRatio, gMidiFile->Division);
}

// テンポマップを使用して指定されたティックまでの時間を計算する関数 - 修正
static int calculateAccurateTimeFromTick(uint64_t targetTick) {
    if (!gMidiFile || !gMidiFile->Running || gMidiFile->Division <= 0) {
        return 0;
    }

    // テンポマップが初期化されていない場合は初期化
    if (!tempoMapInitialized || tempoMap.count == 0) {
        initTempoMap();

        // 現在のテンポがデフォルトと異なる場合は記録
        if (gMidiFile->CurrentTempo > 0 && gMidiFile->CurrentTempo != 500000) {
            recordTempoChange(0, gMidiFile->CurrentTempo);
        }
    }

    // 対象ティックが0の場合は0を返す
    if (targetTick == 0) {
        return 0;
    }

    double totalTimeMs = 0.0;
    uint64_t lastTick = 0;
    long long lastTempo = 500000; // デフォルトテンポ
    int division = gMidiFile->Division;

    // 各テンポ変更ポイント間の時間を計算して合計
    for (int i = 0; i < tempoMap.count; i++) {
        uint64_t currentTick = tempoMap.changes[i].tick;
        long long currentTempo = tempoMap.changes[i].tempo;

        // 無効なテンポ値は使用しない
        if (currentTempo <= 0) {
            currentTempo = 500000; // デフォルトテンポを使用
        }

        // 対象ティック以前のテンポ変更のみ処理
        if (currentTick <= targetTick) {
            // 前回のテンポ変更点から現在のテンポ変更点までの時間を計算
            if (i > 0 && currentTick > lastTick) {
                uint64_t tickDuration = currentTick - lastTick;
                // 計算式: (tick数 * テンポ値) / (division * 1000)
                double segmentTimeMs = ((double)tickDuration * (double)lastTempo) /
                                      ((double)division * 1000.0);
                totalTimeMs += segmentTimeMs;
            }

            lastTick = currentTick;
            lastTempo = currentTempo;
        } else {
            // 対象ティックを超えたらループを抜ける
            break;
        }
    }

    // 最後のテンポ変更点から目標ティックまでの時間を計算
    if (targetTick > lastTick) {
        uint64_t tickDuration = targetTick - lastTick;
        // 計算式: (tick数 * テンポ値) / (division * 1000)
        double segmentTimeMs = ((double)tickDuration * (double)lastTempo) /
                              ((double)division * 1000.0);
        totalTimeMs += segmentTimeMs;
    }

    return (int)totalTimeMs;
}

// 現在の再生位置をミリ秒で返す - BPM倍率の影響を完全に排除
int getCurrentMidiTimeMs() {
    if (!gMidiFile || !gMidiFile->Running) {
        return 0;
    }

    if (gMidiFile->Division <= 0) {
        return 0;
    }

    if (gMidiFile->CurrentTick > UINT64_MAX / 2) {
        return 0;
    }

    int timeMs = calculateAccurateTimeFromTick(gMidiFile->CurrentTick);

    // 相対時間モードの場合は開始位置からの経過時間を計算
    if (useRelativeTime && relativeStartTimeMs > 0) {
        return timeMs - relativeStartTimeMs;
    }

    return timeMs;
}

// MIDIの総再生時間をミリ秒で返す - BPM倍率の影響を完全に排除
int getTotalMidiTimeMs() {
    if (!gMidiFile || !gMidiFile->Running) {
        return 0;
    }

    if (gMidiFile->Division <= 0 || gMidiFile->TotalTicks <= 0) {
        return 0;
    }

    if (gMidiFile->TotalTicks > UINT64_MAX / 2) {
        return 0;
    }

    // キャッシュがあればそれを返す
    if (cachedTotalTimeMs >= 0) {
        return cachedTotalTimeMs;
    }

    // 計算して結果をキャッシュ
    cachedTotalTimeMs = calculateAccurateTimeFromTick(gMidiFile->TotalTicks);
    return cachedTotalTimeMs;
}

// MIDIファイルがロードされているかを返す
bool isMidiLoaded() {
    // MIDIファイルが存在してトラックデータが初期化されていれば有効とみなす
    // Running状態に依存せず、ファイルの存在だけでtrueを返すように修正
    return gMidiFile != NULL && gMidiFile->TrackCount > 0 && gMidiFile->TotalTicks > 0;
}

// 外部関数の宣言 - 正しいシグネチャで統一
extern void playerSeekToPosition(MIDIFile *midiFile, uint64_t targetTick);

// 再生位置を変更する関数 (0.0～1.0) - テンポマップの更新を追加
bool setMidiProgress(float progress) {
    if (!gMidiFile || gMidiFile->TotalTicks <= 0) {
        return false;
    }

    // 範囲を制限
    if (progress < 0.0f) progress = 0.0f;
    if (progress > 1.0f) progress = 1.0f;

    // 新しいティック位置を計算（相対時間モードの場合は相対位置から計算）
    uint64_t newTick;

    if (useRelativeTime) {
        // 相対モードでは基準位置からの相対的な位置を計算
        uint64_t totalRelativeTicks = gMidiFile->TotalTicks - relativeStartTick;
        newTick = relativeStartTick + (uint64_t)(progress * totalRelativeTicks);
    } else {
        // 通常モードでは全体に対する割合
        newTick = (uint64_t)(progress * gMidiFile->TotalTicks);
    }

    uint64_t currentTick = gMidiFile->CurrentTick;

    // 大きな変化がない場合はスキップ
    if (llabs((int64_t)newTick - (int64_t)currentTick) < 5) {
        return true;
    }

    // シーク前のテンポを保存
    long long oldTempo = gMidiFile->CurrentTempo;

    // 再生中だったかを保存
    bool was_playing = !isMidiPaused();

    // 終了状態を判定するためにRunning状態を確認
    bool was_stopped = !gMidiFile->Running;

    // もし停止状態なら、シークのために再度Runningをtrueに設定
    if (was_stopped) {
        gMidiFile->Running = true;
    }

    // 再生中なら一時停止する
    if (was_playing) {
        toggleMidiPlayPause();
        sleepNanos(20000000); // 20ms待機
    }

    // すべてのノートをオフにする
    if (KDMSendDirectData) {
        for (int ch = 0; ch < 16; ch++) {
            uint32_t msg1 = create_midi_message(0xB0, ch, 120, 0);
            uint32_t msg2 = create_midi_message(0xB0, ch, 123, 0);
            KDMSendDirectData(msg1);
            KDMSendDirectData(msg2);
        }
    }

    // シーク操作を実行
    playerSeekToPosition(gMidiFile, newTick);

    // シーク操作後のテンポをチェック
    long long newTempo = gMidiFile->CurrentTempo;

    // シーク後のテンポ変更をテンポマップに記録
    if (newTempo > 0 && newTempo != oldTempo) {
        recordTempoChange(gMidiFile->CurrentTick, newTempo);
    }

    // シーク操作後にinitialTempoを保存（最初のシーク時のみ）
    if (initialTempo <= 0 && newTempo > 0) {
        initialTempo = newTempo;
    }

    // シーク操作後にtickToMsRatio初期化（最初のシーク時のみ）
    if (tickToMsRatio <= 0.0) {
        initializeTimeCalculation();
    }

    // より長い待機時間を確保してテンポ適用を確実に
    sleepNanos(150000000); // 150ms待機

    // MIDIバッファをリセットしてクリーンな状態で再開
    if (KDMReset) {
        KDMReset();
        sleepNanos(10000000); // リセット後少し待機
    }

    // 再生を再開（元々再生中だった場合のみ）
    if (was_playing) {
        sleepNanos(30000000); // 追加の30ms待機
        toggleMidiPlayPause();
    }

    return true;
}

// 現在のBPMを取得（実際のテンポ × 速度倍率） - デバッグ出力を削除
float getCurrentBPM() {
    if (!gMidiFile || !gMidiFile->Running) {
        return 120.0f; // デフォルトBPM
    }

    // テンポ値の妥当性チェック
    if (gMidiFile->CurrentTempo <= 0) {
        gMidiFile->CurrentTempo = 500000; // デフォルトテンポに修正
    }

    // マイクロ秒/四分音符からBPMに変換
    float baseBPM = 60000000.0f / (float)gMidiFile->CurrentTempo;

    // 現在の速度倍率を適用
    return baseBPM * bpmMultiplier;
}

// bpmMultiplier 変数へのアクセサ関数 - 他のファイルから参照用
float getBpmMultiplier(void) {
    return bpmMultiplier;
}

// 再生速度を変更（BPM倍率）
bool setPlaybackBPM(float multiplier) {
    // 範囲制限（0.25倍〜5.0倍）
    if (multiplier < 0.25f) multiplier = 0.25f;
    if (multiplier > 5.0f) multiplier = 5.0f;

    bpmMultiplier = multiplier;
    return true;
}

// MIDIファイルの時間を計算する改良版関数（分:秒形式）
void getMidiTimeString(char* buffer, int bufferSize, bool getTotalTime) {
    if (!buffer || bufferSize < 8 || !gMidiFile || !gMidiFile->Running) {
        if (buffer && bufferSize > 0) {
            strncpy(buffer, "00:00", bufferSize - 1);
            buffer[bufferSize - 1] = '\0';
        }
        return;
    }

    // ミリ秒で時間を取得
    int timeMs;

    if (getTotalTime) {
        timeMs = getTotalMidiTimeMs();
    } else {
        // 現在時間は相対/絶対モードに応じて取得
        timeMs = getCurrentMidiTimeMs();

        // 相対モードで負の値になった場合は0とする
        if (useRelativeTime && timeMs < 0) {
            timeMs = 0;
        }
    }

    // ミリ秒から分:秒形式に変換
    int totalSeconds = timeMs / 1000;
    int minutes = totalSeconds / 60;
    int seconds = totalSeconds % 60;

    // 相対時間モードかつ現在時間の場合は符号を追加
    if (useRelativeTime && !getTotalTime && buffer && bufferSize >= 9) {
        snprintf(buffer, bufferSize, "%02d:%02d", minutes, seconds);
    } else {
        // 通常のフォーマット
        snprintf(buffer, bufferSize, "%02d:%02d", minutes, seconds);
    }
}

// 現在の再生位置の時間をミリ秒で取得する
int getCurrentTimeMs() {
    if (!gMidiFile || !gMidiFile->Running) {
        return 0;
    }

    // 実際のティック位置からテンポマップを考慮した経過時間を計算
    int timeMs = calculateElapsedTime(gMidiFile->CurrentTick);

    // 相対時間モードの場合は開始位置からの経過時間を計算
    if (useRelativeTime) {
        int startTimeMs = calculateElapsedTime(relativeStartTick);
        return timeMs - startTimeMs;
    }

    return timeMs;
}

// 指定ティックまでの経過時間を計算（テンポマップを考慮）
int calculateElapsedTime(uint64_t currentTick) {
    if (!gMidiFile || !gMidiFile->Running || gMidiFile->Division <= 0) {
        return 0;
    }

    double totalMs = 0.0;
    // lastTick変数を削除（未使用）
    long long lastTempo = 500000; // デフォルトテンポ (120 BPM)

    // テンポマップが空の場合はデフォルトテンポを使用して計算
    if (tempoMap.count == 0) {
        double ms = (double)currentTick * lastTempo / gMidiFile->Division / 1000.0;
        return (int)(ms / bpmMultiplier);
    }

    // 最初の変更点が0ティックでなければ、初期区間を追加計算
    if (tempoMap.changes[0].tick > 0) {
        uint64_t initialSegmentTicks = currentTick < tempoMap.changes[0].tick ?
                                       currentTick : tempoMap.changes[0].tick;
        totalMs += (double)initialSegmentTicks * lastTempo / gMidiFile->Division / 1000.0;
        // lastTickの更新を削除

        // 処理対象のティックが最初のテンポ変更前なら終了
        if (currentTick <= tempoMap.changes[0].tick) {
            return (int)(totalMs / bpmMultiplier);
        }
    }

    // テンポマップをスキャンして各区間の時間を計算
    for (int i = 0; i < tempoMap.count; i++) {
        // テンポを更新
        lastTempo = tempoMap.changes[i].tempo > 0 ? tempoMap.changes[i].tempo : 500000;

        // 次の区間の終点（次のテンポ変更点または目的ティック）
        uint64_t nextTick = (i < tempoMap.count - 1) ?
                            tempoMap.changes[i + 1].tick :
                            gMidiFile->TotalTicks;

        // 現在のティックが区間内にある場合は区間の一部だけ計算
        if (currentTick <= nextTick) {
            uint64_t segmentTicks = currentTick - tempoMap.changes[i].tick;
            totalMs += (double)segmentTicks * lastTempo / gMidiFile->Division / 1000.0;
            break;
        }
        // それ以外は区間全体を計算
        else {
            uint64_t segmentTicks = nextTick - tempoMap.changes[i].tick;
            totalMs += (double)segmentTicks * lastTempo / gMidiFile->Division / 1000.0;
        }
    }

    // BPM倍率を適用
    return (int)(totalMs / bpmMultiplier);
}

// テンポマップをソートする関数
void sortTempoMap(void) {
    // バブルソートでティック順に並べ替え
    for (int i = 0; i < tempoMap.count - 1; i++) {
        for (int j = 0; j < tempoMap.count - i - 1; j++) {
            if (tempoMap.changes[j].tick > tempoMap.changes[j+1].tick) {
                // 構造体全体を入れ替え
                TempoChange temp = tempoMap.changes[j];
                tempoMap.changes[j] = tempoMap.changes[j+1];
                tempoMap.changes[j+1] = temp;
            }
        }
    }
}

// テンポマップの内容をデバッグ出力する関数
void debugPrintTempoMap() {
    //fprintf(stderr, "Tempo Map Entries: %d\n", tempoMap.count);
    if (tempoMap.count == 0) {
        //fprintf(stderr, "  [Empty tempo map - using default 120 BPM]\n");
        return;
    }

    // テンポマップの各エントリを出力
    for (int i = 0; i < tempoMap.count; i++) {
        long long tempo = tempoMap.changes[i].tempo;
        // 未使用変数警告を回避（コメントアウトされたデバッグ出力で使用）
        (void)tempo;

        // 変数を使用するコメント部分を残す
        //fprintf(stderr, "  [%d] Tick: %llu, Tempo: %lld (%.2f BPM)",
        //        i, (unsigned long long)tempoMap.changes[i].tick, tempo,
        //        tempo > 0 ? 60000000.0 / (double)tempo : 120.0);

        // 各区間の時間情報も追加（最後のエントリ以外）
        if (i < tempoMap.count - 1) {
            uint64_t segmentTicks = tempoMap.changes[i+1].tick - tempoMap.changes[i].tick;
            // 未使用変数警告を回避（コメントアウトされたデバッグ出力で使用）
            (void)segmentTicks;

            //fprintf(stderr, ", Segment length: %llu ticks (%.2f ms)",
            //        (unsigned long long)segmentTicks,
            //        (double)segmentTicks * tempo / (gMidiFile ? gMidiFile->Division : 480) / 1000.0);
        }
        //fprintf(stderr, "\n");
    }

    // BPM統計情報を追加
    float minBpm = 0.0f, maxBpm = 0.0f, avgBpm = 0.0f;
    getMidiBpmInfo(&minBpm, &maxBpm, &avgBpm);
    //fprintf(stderr, "MIDI BPM Info: Min=%.2f, Max=%.2f, Average=%.2f BPM\n",
    //        minBpm, maxBpm, avgBpm);

    //fprintf(stderr, "MIDI Division: %d ticks/quarter-note\n",
    //        gMidiFile ? gMidiFile->Division : 0);
}

// 合計時間をミリ秒で取得する改良版関数
int getMidiTotalTimeMs() {
    if (!gMidiFile || !gMidiFile->Running) {
        return 0;
    }

    // キャッシュされた結果があればそれを返す
    if (cachedTotalTimeMs >= 0) {
        return cachedTotalTimeMs;
    }

    // MIDIファイル全体をシミュレートして時間を計算
    double totalMs = simulateMidiFileDuration();

    // 結果をキャッシュして返す
    cachedTotalTimeMs = (int)totalMs;
    return cachedTotalTimeMs;
}

// MIDIファイルの総再生時間をシミュレートして計算 - テンポ変更に対応
double simulateMidiFileDuration() {
    if (!gMidiFile || (gMidiFile->Division <= 0)) {
        return 0.0;
    }

    // テンポマップをソート
    sortTempoMap();

    double totalMs = 0.0;
    long long lastTempo = 500000; // デフォルトテンポ (120 BPM)

    // テンポマップが空の場合は単純計算
    if (tempoMap.count == 0) {
        double ms = (double)gMidiFile->TotalTicks * lastTempo / gMidiFile->Division / 1000.0;
        //fprintf(stderr, "No tempo changes, using default 120 BPM: %.2f ms total\n", ms);
        return ms / bpmMultiplier;
    }

    // 最初のテンポ変更点が0ティックでなければ、初期区間を計算
    if (tempoMap.changes[0].tick > 0) {
        uint64_t delta = tempoMap.changes[0].tick;
        double segmentMs = (double)delta * lastTempo / gMidiFile->Division / 1000.0;
        totalMs += segmentMs;
        //fprintf(stderr, "  Initial segment: Tick 0-%llu (%.2f BPM): %.2f ms\n",
        //        (unsigned long long)delta, 60000000.0 / (double)lastTempo, segmentMs);
    }

    // 各テンポ区間を処理
    for (int i = 0; i < tempoMap.count; i++) {
        // 現在のテンポを保存（無効値チェック付き）
        lastTempo = tempoMap.changes[i].tempo > 0 ? tempoMap.changes[i].tempo : 500000;

        // 次の区間終点の定義
        uint64_t nextEndTick;
        if (i < tempoMap.count - 1) {
            nextEndTick = tempoMap.changes[i + 1].tick;
        } else {
            nextEndTick = gMidiFile->TotalTicks;
        }

        // 区間の長さを計算
        uint64_t segmentTicks = nextEndTick - tempoMap.changes[i].tick;
        double segmentMs = (double)segmentTicks * lastTempo / gMidiFile->Division / 1000.0;
        totalMs += segmentMs;

        //fprintf(stderr, "  Segment %d: Tick %llu-%llu (%.2f BPM): %.2f ms\n",
        //        i, (unsigned long long)tempoMap.changes[i].tick,
        //        (unsigned long long)nextEndTick,
        //        60000000.0 / (double)lastTempo, segmentMs);
    }

    // BPM倍率を適用して最終結果を返す
    double finalDuration = totalMs / bpmMultiplier;

    // BPM情報を出力
    float minBpm = 0.0f, maxBpm = 0.0f, avgBpm = 0.0f;
    getMidiBpmInfo(&minBpm, &maxBpm, &avgBpm);
    //fprintf(stderr, "MIDI BPM Range: %.2f-%.2f BPM, Weighted Average: %.2f BPM\n",
    //        minBpm, maxBpm, avgBpm);

    //fprintf(stderr, "Total MIDI duration: %.2f ms (%.2f seconds) with BPM multiplier %.2f\n",
    //        finalDuration, finalDuration / 1000.0, bpmMultiplier);

    return finalDuration;
}

// 指定したティック位置でのテンポを取得
long long getCurrentTempoAtTick(uint64_t tick) {
    // テンポマップが空の場合はデフォルトテンポを返す
    if (tempoMap.count == 0) {
        return 500000;
    }

    // 最後に見つかったテンポを保持
    long long lastTempo = 500000;

    // テンポマップから指定ティックに適用されるテンポを探す
    for (int i = 0; i < tempoMap.count; i++) {
        if (tempoMap.changes[i].tick <= tick) {
            lastTempo = tempoMap.changes[i].tempo;
        } else {
            // このティックより後のテンポ変更は関係ない
            break;
        }
    }

    return lastTempo;
}

// 曲全体の平均BPMを計算する関数
float getAverageBPM() {
    if (!gMidiFile || !gMidiFile->Running || tempoMap.count == 0) {
        return 120.0f; // デフォルト値
    }

    // テンポマップが最新になっていることを確認
    sortTempoMap();

    double totalTimeMs = 0.0;
    double weightedBpmSum = 0.0;

    long long lastTempo = 500000; // デフォルト120BPM

    // 最初の変更点が0ティックでなければ、初期区間を考慮
    if (tempoMap.changes[0].tick > 0) {
        double initialSegmentMs = (double)tempoMap.changes[0].tick * lastTempo /
                                 gMidiFile->Division / 1000.0;
        totalTimeMs += initialSegmentMs;
        weightedBpmSum += (60000000.0 / lastTempo) * initialSegmentMs;
    }

    // 各テンポ区間を処理
    for (int i = 0; i < tempoMap.count; i++) {
        lastTempo = tempoMap.changes[i].tempo > 0 ? tempoMap.changes[i].tempo : 500000;
        double bpm = 60000000.0 / lastTempo;

        // 区間の終点を決定
        uint64_t nextTick = (i < tempoMap.count - 1) ?
                           tempoMap.changes[i + 1].tick :
                           gMidiFile->TotalTicks;

        // 区間の時間を計算
        double segmentTicks = nextTick - tempoMap.changes[i].tick;
        double segmentMs = segmentTicks * lastTempo / gMidiFile->Division / 1000.0;

        // 重み付けした合計を計算（時間が長い区間ほど重視）
        totalTimeMs += segmentMs;
        weightedBpmSum += bpm * segmentMs;
    }

    // 合計時間で割って平均BPMを取得
    if (totalTimeMs > 0.0) {
        return (float)(weightedBpmSum / totalTimeMs);
    }

    return 120.0f; // フォールバック値
}

// MIDIファイルの最小・最大・平均BPMを取得
void getMidiBpmInfo(float *minBpm, float *maxBpm, float *avgBpm) {
    // デフォルト値を設定
    float min = 120.0f;
    float max = 120.0f;
    float avg = 120.0f;

    if (gMidiFile && gMidiFile->Running) {
        // 最小・最大BPMを探す
        if (tempoMap.count > 0) {
            min = FLT_MAX;
            max = 0.0f;

            for (int i = 0; i < tempoMap.count; i++) {
                long long tempo = tempoMap.changes[i].tempo;
                if (tempo <= 0) continue; // 無効なテンポはスキップ

                float bpm = 60000000.0f / (float)tempo;
                if (bpm < min) min = bpm;
                if (bpm > max) max = bpm;
            }

            // 有効な値がない場合はデフォルト値
            if (min == FLT_MAX) min = 120.0f;
            if (max == 0.0f) max = 120.0f;
        }

        // 平均BPMを計算
        avg = getAverageBPM();
    }

    // 結果を返す
    if (minBpm) *minBpm = min;
    if (maxBpm) *maxBpm = max;
    if (avgBpm) *avgBpm = avg;
}

// 与えられた時間(ミリ秒)からティック位置を計算する
uint64_t getTickFromTime(int timeMs) {
    if (!gMidiFile || !gMidiFile->Running ||
        gMidiFile->Division <= 0 || timeMs < 0) {
        return 0;
    }

    // テンポマップが空の場合は単純計算
    if (tempoMap.count == 0) {
        long long tempo = 500000; // デフォルトテンポ
        // ミリ秒からティックへの変換
        return (uint64_t)((double)timeMs * gMidiFile->Division * 1000.0 / tempo);
    }

    // ティックを探索
    uint64_t currentTick = 0;
    int currentTimeMs = 0;
    long long currentTempo = 500000; // デフォルトテンポから開始

    // テンポマップの最初の変更点より前の時間を計算
    if (tempoMap.changes[0].tick > 0 && timeMs > 0) {
        double ticksPerMs = ((double)gMidiFile->Division * 1000.0) / currentTempo;
        if (currentTimeMs < timeMs) {
            uint64_t initialTicks = tempoMap.changes[0].tick;
            double initialTimeMs = (double)initialTicks * currentTempo /
                                 (gMidiFile->Division * 1000.0);

            if (timeMs < initialTimeMs) {
                // 目標時間が最初のテンポ変更より前
                return (uint64_t)(timeMs * ticksPerMs);
            } else {
                currentTick = initialTicks;
                currentTimeMs = (int)initialTimeMs;
            }
        }
    }

    // テンポマップをスキャンして正確なティック位置を計算
    for (int i = 0; i < tempoMap.count; i++) {
        currentTempo = tempoMap.changes[i].tempo;
        if (currentTempo <= 0) currentTempo = 500000; // 無効なテンポの場合

        uint64_t nextTick;
        if (i < tempoMap.count - 1) {
            nextTick = tempoMap.changes[i + 1].tick;
        } else {
            nextTick = gMidiFile->TotalTicks;
        }

        // この区間の時間を計算
        double segmentTimeMs = (double)(nextTick - currentTick) * currentTempo /
                             (gMidiFile->Division * 1000.0);

        if (currentTimeMs + segmentTimeMs >= timeMs) {
            // 目標時間がこの区間内にある
            double remainingMs = timeMs - currentTimeMs;
            double ticksPerMs = ((double)gMidiFile->Division * 1000.0) / currentTempo;
            return currentTick + (uint64_t)(remainingMs * ticksPerMs);
        }

        currentTick = nextTick;
        currentTimeMs += (int)segmentTimeMs;
    }

    // 目標時間が曲の長さを超えている場合は最大ティック数を返す
    return gMidiFile->TotalTicks;
}

// ノート情報を前処理する関数
void preprocessNoteInfo(uint8_t* data, uint32_t length, uint64_t tick) {
    // 未使用パラメータ警告を回避
    (void)tick;

    if (!data || length == 0) return;

    // データコピーを作成して処理
    char* text = new char[length + 1];
    if (!text) return;

    memcpy(text, data, length);
    text[length] = '\0';

    // ノート情報を含むテキストを解析
    // ここでは単純にメタデータとして保存
    //fprintf(stderr, "Note info at tick %llu: %s\n",
    //        (unsigned long long)tick, text);

    delete[] text;
}

// ノートのタイミングを更新する関数
void updateNoteTimings(uint64_t currentTick, long long tempo) {
    if (!gMidiFile) return;

    // 未使用変数に (void) をつけて警告を抑制
    (void)currentTick;
    double ticksPerBeat = gMidiFile->Division;
    (void)ticksPerBeat; // 未使用変数の警告を抑制

    double microsecondsPerBeat = tempo;
    (void)microsecondsPerBeat; // 未使用変数の警告を抑制

    // ...existing code...
}

// 追加：一時停止状態を設定する関数
void setMidiPaused(bool paused) {
    midi_paused = paused;
}
