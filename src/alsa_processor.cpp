#include "alsa_processor.h"
#include "alsa_midi.h"
#include "midi_utils.h"
#include <stdio.h>

// ALSA処理専用の初期化フラグ
static bool alsaProcessorInitialized = false;

// ALSA処理の初期化
bool initializeAlsaProcessor(void) {
    if (alsaProcessorInitialized) {
        return true;
    }

    // ALSA MIDIライブラリを初期化
    if (!initializeAlsaMidi()) {
        fprintf(stderr, "ALSA MIDIの初期化に失敗しました\n");
        return false;
    }

    alsaProcessorInitialized = true;
    fprintf(stderr, "ALSA処理プロセッサーが初期化されました\n");
    return true;
}

// ALSA処理の終了処理
void terminateAlsaProcessor(void) {
    if (!alsaProcessorInitialized) {
        return;
    }

    // ALSA MIDIを終了
    terminateAlsaMidi();
    
    alsaProcessorInitialized = false;
    fprintf(stderr, "ALSA処理プロセッサーが終了されました\n");
}

// ALSA処理の状態取得
bool isAlsaProcessorInitialized(void) {
    return alsaProcessorInitialized;
}

// MIDIノートONイベントでALSA音声出力
void processAlsaNoteOn(uint8_t channel, uint8_t note, uint8_t velocity, int trackNumber) {
    (void)trackNumber; // 未使用パラメータの警告を抑制

    if (!alsaProcessorInitialized) {
        return;
    }

    // ALSAでノートON送信
    uint32_t msg = create_midi_message(0x90, channel, note, velocity);
    alsaSendDirectData(msg);
}

// MIDIノートOFFイベントでALSA音声出力
void processAlsaNoteOff(uint8_t channel, uint8_t note, int trackNumber) {
    (void)trackNumber; // 未使用パラメータの警告を抑制

    if (!alsaProcessorInitialized) {
        return;
    }

    // ALSAでノートOFF送信
    uint32_t msg = create_midi_message(0x80, channel, note, 0);
    alsaSendDirectData(msg);
}

// MIDIコントロールチェンジイベントでALSA送信
void processAlsaControlChange(uint8_t channel, uint8_t control, uint8_t value) {
    if (!alsaProcessorInitialized) {
        return;
    }

    // ALSAでコントロールチェンジ送信
    uint32_t msg = create_midi_message(0xB0, channel, control, value);
    alsaSendDirectData(msg);
}
