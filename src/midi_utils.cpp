#include <stdint.h>
#include <cstdlib> // malloc(), free() のために追加
#include <cstring> // memcpy() のために追加
#include <stdio.h>  // fprintf(), stderr のために追加
#include "midi_utils.h"
#include "OmniMIDI.h"
#include "midiplayer/utils.h" // TempoMap定義の参照

// バイトスワップ関数
uint16_t swap_uint16(uint16_t val)
{
    return (val << 8) | (val >> 8);
}

uint32_t swap_uint32(uint32_t val)
{
    val = ((val << 8) & 0xFF00FF00) | ((val >> 8) & 0x00FF00FF);
    return (val << 16) | (val >> 16);
}

// MIDI形式のメッセージを作成
uint32_t create_midi_message(uint8_t status, uint8_t channel, uint8_t data1, uint8_t data2)
{
    return ((status & 0xF0) | (channel & 0x0F)) | (data1 << 8) | (data2 << 16);
}

// ノートONメッセージを送信
void send_note_on(uint8_t channel, uint8_t note, uint8_t velocity)
{
    if (KDMSendDirectData) {
        uint32_t msg = create_midi_message(0x90, channel, note, velocity);
        KDMSendDirectData(msg);
    }
}

// ノートOFFメッセージを送信
void send_note_off(uint8_t channel, uint8_t note, uint8_t velocity)
{
    if (KDMSendDirectData) {
        uint32_t msg = create_midi_message(0x80, channel, note, velocity);
        KDMSendDirectData(msg);
    }
}

// プログラムチェンジメッセージを送信
void send_program_change(uint8_t channel, uint8_t program)
{
    if (KDMSendDirectData) {
        uint32_t msg = create_midi_message(0xC0, channel, program, 0);
        KDMSendDirectData(msg);

        // プログラム変更をグローバル状態に記録（外部へのエクスポートのため）
        #ifdef INTERNAL_MIDIPLAYER_IMPLEMENTATION
        if (gMidiFile) {
            recordProgramChange(channel, program, gMidiFile->CurrentTick);
        }
        #endif
    }
}

// SysExメッセージを送信する関数
void send_sysex_message(uint8_t* data, uint32_t length) {
    if (!data || length == 0) {
        fprintf(stderr, "Warning: Attempted to send invalid SysEx (NULL or zero length)\n");
        return;  // 無効なデータは送信しない
    }

    if (KDMSendLongData) {  // 関数ポインタがNULLでないことを確認
        // バッファをコピーして送信（データ改変を防止）
        uint8_t* buffer = new uint8_t[length];
        if (buffer) {
            memcpy(buffer, data, length);

            // 最初の数バイトを表示
            fprintf(stderr, "Sending SysEx: %02X %02X %02X... (total: %u bytes)\n",
                    data[0],
                    length > 1 ? data[1] : 0,
                    length > 2 ? data[2] : 0,
                    length);

            KDMSendLongData(buffer, length);
            delete[] buffer;
        } else {
            fprintf(stderr, "Error: Failed to allocate memory for SysEx message (%u bytes)\n", length);
        }
    } else {
        // SysEx機能が利用できない場合の警告
        fprintf(stderr, "Warning: KDMSendLongData function not available, SysEx message discarded\n");
    }
}

#ifdef _WIN32
// Windows環境用のグローバル変数の定義
wchar_t gWideFilePath[MAX_PATH] = {0};
bool gUseWidePath = false;
#endif

// その他のユーティリティ関数...
