#include <stdint.h>
#include <cstdlib> // malloc(), free() のために追加
#include <cstring> // memcpy() のために追加
#include <stdio.h>  // fprintf(), stderr のために追加
#include "midi_utils.h"
#include "alsa_midi.h"
#include "midiplayer/utils.h" // TempoMap定義の参照

// バイトスワップ関数
uint16_t swap_uint16(uint16_t val)
{
    return (val << 8) | (val >> 8);
}

uint32_t swap_uint32(uint32_t val)
{
    val = ((val << 8) & 0xFF00FF00) | ((val >> 8) & 0x00FF00FF);
    return (val << 16) | (val >> 16);
}

// MIDI形式のメッセージを作成
uint32_t create_midi_message(uint8_t status, uint8_t channel, uint8_t data1, uint8_t data2)
{
    return ((status & 0xF0) | (channel & 0x0F)) | (data1 << 8) | (data2 << 16);
}

// ノートONメッセージを送信
void send_note_on(uint8_t channel, uint8_t note, uint8_t velocity)
{
    uint32_t msg = create_midi_message(0x90, channel, note, velocity);
    alsaSendDirectData(msg);
}

// ノートOFFメッセージを送信
void send_note_off(uint8_t channel, uint8_t note, uint8_t velocity)
{
    uint32_t msg = create_midi_message(0x80, channel, note, velocity);
    alsaSendDirectData(msg);
}

// プログラムチェンジメッセージを送信
void send_program_change(uint8_t channel, uint8_t program)
{
    uint32_t msg = create_midi_message(0xC0, channel, program, 0);
    alsaSendDirectData(msg);

    // プログラム変更をグローバル状態に記録（外部へのエクスポートのため）
    #ifdef INTERNAL_MIDIPLAYER_IMPLEMENTATION
    if (gMidiFile) {
        recordProgramChange(channel, program, gMidiFile->CurrentTick);
    }
    #endif
}

// SysExメッセージを送信する関数
void send_sysex_message(uint8_t* data, uint32_t length) {
    if (!data || length == 0) {
        fprintf(stderr, "Warning: Attempted to send invalid SysEx (NULL or zero length)\n");
        return;  // 無効なデータは送信しない
    }

    // 最初の数バイトを表示
    fprintf(stderr, "Sending SysEx: %02X %02X %02X... (total: %u bytes)\n",
            data[0],
            length > 1 ? data[1] : 0,
            length > 2 ? data[2] : 0,
            length);

    alsaSendLongData(data, length);
}

// その他のユーティリティ関数...
