// Windows API includes
#ifdef _WIN32
    #define WIN32_LEAN_AND_MEAN
    #define NOMINMAX
    #include <windows.h>
#endif
#include "midi_processor.h"
// Removed visualization and delay includes
#include "alsa_processor.h"
#include <pthread.h>

// 統合MIDI処理の初期化フラグ
static bool midiProcessorInitialized = false;

// 統合MIDI処理の初期化
bool initializeMidiProcessor(void) {
    if (midiProcessorInitialized) {
        return true;
    }

    // ALSA処理モジュールを初期化
    if (!initializeAlsaProcessor()) {
        return false;
    }

    // Delay system removed

    midiProcessorInitialized = true;
    return true;
}

// 統合MIDI処理の終了処理
void terminateMidiProcessor(void) {
    if (!midiProcessorInitialized) {
        return;
    }

    // 各処理モジュールを終了
    terminateAlsaProcessor();

    midiProcessorInitialized = false;
}

// MIDIノートONイベントの統合処理
void processMidiNoteOn(uint8_t channel, uint8_t note, uint8_t velocity, int trackNumber) {
    if (!midiProcessorInitialized) {
        return;
    }

    // ALSA処理のみ実行
    processAlsaNoteOn(channel, note, velocity, trackNumber);
}

// MIDIノートOFFイベントの統合処理
void processMidiNoteOff(uint8_t channel, uint8_t note, int trackNumber) {
    if (!midiProcessorInitialized) {
        return;
    }

    // ALSA処理のみ実行
    processAlsaNoteOff(channel, note, trackNumber);
}

// MIDIコントロールチェンジイベントの統合処理
void processMidiControlChange(uint8_t channel, uint8_t control, uint8_t value) {
    if (!midiProcessorInitialized) {
        return;
    }

    // 現在はALSAのみ処理
    processAlsaControlChange(channel, control, value);
}

// 統合MIDI処理の状態取得
bool isMidiProcessorInitialized(void) {
    return midiProcessorInitialized;
}
