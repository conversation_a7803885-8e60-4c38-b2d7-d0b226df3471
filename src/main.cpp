// Windows API includes
#ifdef _WIN32
    #define WIN32_LEAN_AND_MEAN
    #define NOMINMAX
    #include <windows.h>
    #include <commdlg.h>
#endif

#include <cstdio>
#include <cstdlib>
#include <cmath>
#include <pthread.h>
#include <unistd.h>
#include <stdbool.h>

// 文字化け対策用のデバッグヘッダを追加
#include "debug_utils.h"

#include <cstring>

#include "midi_header.h"
#include "midi_data.h"
#include "midi_utils.h"
#include "midiplayer.h"
#include "midiplayer/loader.h"
#include "midiplayer/utils.h"
#include "midi_processor.h"
#include "thread_args.h"
// GUI functionality removed

bool IS_QUIT = false;

// スレッド間の同期用
volatile bool gui_initialized = false;
volatile bool midi_initialized = false;
pthread_mutex_t sync_mutex = PTHREAD_MUTEX_INITIALIZER;
pthread_cond_t sync_cond = PTHREAD_COND_INITIALIZER;

// 関数プロトタイプの宣言
void play_scale(void);

// GUI functionality removed

// MIDIファイル読み込みラッパー関数
void* midi_load_wrapper(void* arg) {
    DEBUG_LOG("MIDIスレッド開始");

    // コンパイラに応じた例外処理
    #if defined(_MSC_VER) // Microsoft Visual C++の場合
        __try {
            loadMidiFile(arg);
        }
        __except(EXCEPTION_EXECUTE_HANDLER) {
            DEBUG_LOG("MIDIスレッドでエラーが発生しました: %ld", GetExceptionCode());
        }
    #else
        // MinGWなど他のコンパイラでは直接関数を呼び出す
        loadMidiFile(arg);
    #endif

    DEBUG_LOG("MIDIスレッド終了");
    return NULL;
}

int main(int argc, char **argv)
{
    // Windows環境の場合、コンソールのコードページをUTF-8に設定
    #ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
    #endif

    char midiFilePath[MAX_PATH] = {0};
    char *midiFile = NULL;

    #ifdef _WIN32
    if (argc < 2) {
        // WindowsファイルダイアログでMIDIファイルを選択
        OPENFILENAMEW ofn;
        wchar_t szFileW[MAX_PATH] = L"";
        memset(&ofn, 0, sizeof(ofn));
        ofn.lStructSize = sizeof(ofn);
        ofn.hwndOwner = NULL;
        ofn.lpstrFile = szFileW;
        ofn.nMaxFile = MAX_PATH;
        ofn.lpstrFilter = L"MIDI Files (*.mid;*.midi)\0*.mid;*.midi\0All Files (*.*)\0*.*\0";
        ofn.nFilterIndex = 1;
        ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST;

        if (GetOpenFileNameW(&ofn)) {
            // UTF-16からUTF-8への変換を改善
            int len = WideCharToMultiByte(CP_UTF8, 0, szFileW, -1, NULL, 0, NULL, NULL);
            if (len > 0) {
                // 十分なバッファサイズを確保
                char* utf8Path = new char[len + 1];
                if (utf8Path) {
                    if (WideCharToMultiByte(CP_UTF8, 0, szFileW, -1, utf8Path, len, NULL, NULL) > 0) {
                        strncpy(midiFilePath, utf8Path, MAX_PATH - 1);
                        midiFilePath[MAX_PATH - 1] = '\0';
                        midiFile = midiFilePath;
                        fprintf(stderr, "選択されたMIDIファイル: '%s'\n", midiFile);

                        #ifdef _WIN32
                        // ワイド文字のパスを保存（グローバル変数を使用）
                        wcsncpy(gWideFilePath, szFileW, MAX_PATH - 1);
                        gWideFilePath[MAX_PATH - 1] = L'\0';
                        gUseWidePath = true;
                        #endif
                    } else {
                        fprintf(stderr, "UTF-16からUTF-8への変換に失敗しました。エラーコード: %lu\n", GetLastError());
                    }
                    delete[] utf8Path;
                } else {
                    fprintf(stderr, "メモリ割り当てに失敗しました\n");
                }
            } else {
                fprintf(stderr, "ファイル名の変換サイズ計算に失敗しました。エラーコード: %lu\n", GetLastError());
            }
        }
        if (!midiFile) {
            fprintf(stderr, "MIDIファイルが指定されていません\n");
            return 1;
        }
    } else {
        // コマンドライン引数からのファイル名を処理（日本語対応）
        #ifdef _WIN32
        // Windows環境ではコマンドライン引数をUTF-8として処理
        wchar_t wideFilePath[MAX_PATH];
        if (MultiByteToWideChar(CP_UTF8, 0, argv[1], -1, wideFilePath, MAX_PATH) > 0) {
            WideCharToMultiByte(CP_UTF8, 0, wideFilePath, -1, midiFilePath, MAX_PATH, NULL, NULL);
            midiFile = midiFilePath;

            // ワイド文字パスを保存（グローバル変数を使用）
            wcsncpy(gWideFilePath, wideFilePath, MAX_PATH - 1);
            gWideFilePath[MAX_PATH - 1] = L'\0';
            gUseWidePath = true;
        } else {
            midiFile = argv[1]; // 変換失敗時は元のパスを使用
            gUseWidePath = false;
        }
        #else
        midiFile = argv[1];
        #endif
    }
    #else
    if (argc < 2) {
        fprintf(stderr, "Usage: %s midiFile.mid\n", argv[0]);
        return 1;
    }
    midiFile = argv[1];
    #endif

    fprintf(stderr, "C++ used %ld.\n", __cplusplus);
    fprintf(stderr, "MIDIファイル '%s' を読み込みます\n", midiFile);

    // MIDIプレイヤー初期化
    DEBUG_LOG("プログラム状態の初期化");
    initProgramState();

    // 新しいMIDI処理システムの初期化
    DEBUG_LOG("MIDI処理システムの初期化");
    if (!initializeMidiProcessor()) {
        fprintf(stderr, "MIDI処理システムの初期化に失敗しました\n");
        return 1;
    }

    // ALSA MIDIの初期化はmidi_processorで行われるため、ここでは何もしない
    fprintf(stderr, "ALSA MIDI初期化完了\n");

    // 初期化用のミューテックス
    pthread_mutex_init(&sync_mutex, NULL);
    pthread_cond_init(&sync_cond, NULL);

    // GUI functionality removed - proceeding directly to MIDI loading
    fprintf(stderr, "MIDIスレッドを開始します\n");

    // MIDIファイルを読み込んで再生
    pthread_t midi_thread;
    int midi_result = 0;

    struct midiPlayer_load_args *midi_args = new midiPlayer_load_args;
    if (!midi_args) {
        fprintf(stderr, "メモリ割り当てに失敗しました\n");
        return 1;
    }

    midi_args->midiFile = midiFile;

    fprintf(stderr, "MIDIプレーヤースレッドを作成しています...\n");
    midi_result = pthread_create(&midi_thread, NULL, midi_load_wrapper, midi_args);

    if (midi_result != 0) {
        fprintf(stderr, "MIDIプレーヤースレッドの作成に失敗しました: %d\n", midi_result);
        delete midi_args;
        return 1;
    }

    fprintf(stderr, "MIDIプレーヤースレッド作成成功\n");

    DEBUG_LOG("メインスレッド: MIDIスレッドの終了を待機中...");

    // MIDIスレッドが終了するまで待機
    pthread_join(midi_thread, NULL);

    fprintf(stderr, "MIDIプレーヤースレッドが終了しました\n");
    delete midi_args;

    // MIDI処理システムの終了処理
    DEBUG_LOG("MIDI処理システムの終了処理");
    terminateMidiProcessor();

    // リソース解放
    pthread_mutex_destroy(&sync_mutex);
    pthread_cond_destroy(&sync_cond);

    DEBUG_LOG("プログラム正常終了");
    return 0;
}



void play_scale(void) {
    int base_note = 60; // 中央のC
    int notes[] = {0, 2, 4, 5, 7, 9, 11, 12}; // メジャースケール

    for (int i = 0; i < 8; i++) {
        int note = base_note + notes[i];
        send_note_on(0, note, 100); // チャンネル0、ベロシティ100でノートON
        usleep(300000); // 0.3秒待つ
        send_note_off(0, note, 0); // ノートOFF
        usleep(100000); // 0.1秒の間隔
    }
}
