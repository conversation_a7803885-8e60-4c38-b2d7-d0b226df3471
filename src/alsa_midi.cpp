#include "alsa_midi.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <alsa/asoundlib.h>

// ALSA sequencer関連の変数
static snd_seq_t *seq_handle = NULL;
static int seq_port = -1;
static int seq_queue = -1;
static int dest_client = 14;
static int dest_port = 0;
static bool alsa_midi_initialized = false;

// ALSA MIDI初期化
bool initializeAlsaMidi(void) {
    if (alsa_midi_initialized) {
        return true;
    }

    int err;

    // ALSA sequencerを非ブロッキングモードで開く
    err = snd_seq_open(&seq_handle, "default", SND_SEQ_OPEN_OUTPUT, SND_SEQ_NONBLOCK);
    if (err < 0) {
        fprintf(stderr, "ALSA sequencerを開けませんでした: %s\n", snd_strerror(err));
        return false;
    }

    // クライアント名を設定
    snd_seq_set_client_name(seq_handle, "HMP MIDI Player");

    // 出力バッファサイズを設定（大きなMIDIファイル用）
    err = snd_seq_set_output_buffer_size(seq_handle, 65536);
    if (err < 0) {
        fprintf(stderr, "警告: 出力バッファサイズの設定に失敗: %s\n", snd_strerror(err));
    }

    // キューを作成
    seq_queue = snd_seq_alloc_queue(seq_handle);
    if (seq_queue < 0) {
        fprintf(stderr, "警告: キューの作成に失敗: %s\n", snd_strerror(seq_queue));
        seq_queue = -1; // キューなしで続行
    } else {
        // キューを開始
        snd_seq_start_queue(seq_handle, seq_queue, NULL);
    }

    // 出力ポートを作成
    seq_port = snd_seq_create_simple_port(seq_handle, "MIDI Output",
                                          SND_SEQ_PORT_CAP_READ | SND_SEQ_PORT_CAP_SUBS_READ,
                                          SND_SEQ_PORT_TYPE_MIDI_GENERIC);
    if (seq_port < 0) {
        fprintf(stderr, "ALSA出力ポートを作成できませんでした: %s\n", snd_strerror(seq_port));
        snd_seq_close(seq_handle);
        seq_handle = NULL;
        return false;
    }

    // ポート14:0に接続

    err = snd_seq_connect_to(seq_handle, seq_port, dest_client, dest_port);
    if (err < 0) {
        fprintf(stderr, "ポート%d:%dへの接続に失敗しました: %s\n",
                dest_client, dest_port, snd_strerror(err));
        // 接続に失敗しても続行（ポートが存在しない可能性がある）
        fprintf(stderr, "警告: MIDI出力ポートに接続できませんでしたが、続行します\n");
    } else {
        fprintf(stderr, "ALSA MIDI: ポート%d:%dに接続しました\n", dest_client, dest_port);
    }

    alsa_midi_initialized = true;
    fprintf(stderr, "ALSA MIDIが正常に初期化されました\n");
    return true;
}

// ALSA MIDI終了処理
void terminateAlsaMidi(void) {
    if (!alsa_midi_initialized) {
        return;
    }

    if (seq_handle) {
        // 全てのノートオフを送信
        snd_seq_event_t ev;
        snd_seq_ev_clear(&ev);
        snd_seq_ev_set_source(&ev, seq_port);
        snd_seq_ev_set_dest(&ev, dest_client, dest_port);
        snd_seq_ev_set_direct(&ev);

        // 全チャンネルにAll Notes Offを送信
        for (int ch = 0; ch < 16; ch++) {
            snd_seq_ev_set_controller(&ev, ch, 123, 0); // All Notes Off
            snd_seq_event_output(seq_handle, &ev);
        }
        snd_seq_drain_output(seq_handle);

        // キューを停止・解放
        if (seq_queue >= 0) {
            snd_seq_stop_queue(seq_handle, seq_queue, NULL);
            snd_seq_free_queue(seq_handle, seq_queue);
            seq_queue = -1;
        }

        snd_seq_close(seq_handle);
        seq_handle = NULL;
    }

    seq_port = -1;
    seq_queue = -1;
    alsa_midi_initialized = false;
    fprintf(stderr, "ALSA MIDIが終了されました\n");
}

// ALSA MIDI状態確認
bool isAlsaMidiInitialized(void) {
    return alsa_midi_initialized;
}

// MIDIメッセージを送信（32bit形式）
void alsaSendDirectData(uint32_t msg) {
    if (!alsa_midi_initialized || !seq_handle) {
        return;
    }

    snd_seq_event_t ev;
    snd_seq_ev_clear(&ev);
    snd_seq_ev_set_source(&ev, seq_port);
    snd_seq_ev_set_dest(&ev, dest_client, dest_port);
    snd_seq_ev_set_direct(&ev);

    // 32bitメッセージから各バイトを抽出
    uint8_t status = (msg >> 0) & 0xFF;
    uint8_t data1 = (msg >> 8) & 0xFF;
    uint8_t data2 = (msg >> 16) & 0xFF;
    uint8_t channel = status & 0x0F;
    uint8_t command = status & 0xF0;

    switch (command) {
        case 0x80: // Note Off
            snd_seq_ev_set_noteoff(&ev, channel, data1, data2);
            break;
        case 0x90: // Note On
            snd_seq_ev_set_noteon(&ev, channel, data1, data2);
            break;
        case 0xA0: // Polyphonic Key Pressure
            snd_seq_ev_set_keypress(&ev, channel, data1, data2);
            break;
        case 0xB0: // Control Change
            snd_seq_ev_set_controller(&ev, channel, data1, data2);
            break;
        case 0xC0: // Program Change
            snd_seq_ev_set_pgmchange(&ev, channel, data1);
            break;
        case 0xD0: // Channel Pressure
            snd_seq_ev_set_chanpress(&ev, channel, data1);
            break;
        case 0xE0: // Pitch Bend
            {
                int pitch = (data2 << 7) | data1;
                snd_seq_ev_set_pitchbend(&ev, channel, pitch - 8192);
            }
            break;
        default:
            // 未対応のメッセージ
            fprintf(stderr, "警告: 未対応のMIDIメッセージ: 0x%02X\n", command);
            return;
    }

    int err = snd_seq_event_output(seq_handle, &ev);
    if (err < 0) {
        if (err == -EAGAIN) {
            // バッファが満杯の場合、少し待ってから再試行
            usleep(1000); // 1ms待機
            snd_seq_drain_output(seq_handle);
            err = snd_seq_event_output(seq_handle, &ev);
            if (err < 0 && err != -EAGAIN) {
                fprintf(stderr, "MIDIイベント送信エラー: %s\n", snd_strerror(err));
            }
        } else {
            fprintf(stderr, "MIDIイベント送信エラー: %s\n", snd_strerror(err));
        }
    }

    // 定期的にバッファをフラッシュ（すべてのイベントではなく）
    static int flush_counter = 0;
    if (++flush_counter % 100 == 0) {
        snd_seq_drain_output(seq_handle);
    }
}

// SysExメッセージを送信
void alsaSendLongData(uint8_t* data, uint32_t size) {
    if (!alsa_midi_initialized || !seq_handle || !data || size == 0) {
        return;
    }

    snd_seq_event_t ev;
    snd_seq_ev_clear(&ev);
    snd_seq_ev_set_source(&ev, seq_port);
    snd_seq_ev_set_dest(&ev, dest_client, dest_port);
    snd_seq_ev_set_direct(&ev);

    snd_seq_ev_set_sysex(&ev, size, data);

    int err = snd_seq_event_output(seq_handle, &ev);
    if (err < 0) {
        if (err == -EAGAIN) {
            // バッファが満杯の場合、少し待ってから再試行
            usleep(1000); // 1ms待機
            snd_seq_drain_output(seq_handle);
            err = snd_seq_event_output(seq_handle, &ev);
            if (err < 0 && err != -EAGAIN) {
                fprintf(stderr, "SysExメッセージ送信エラー: %s\n", snd_strerror(err));
            }
        } else {
            fprintf(stderr, "SysExメッセージ送信エラー: %s\n", snd_strerror(err));
        }
    }
    snd_seq_drain_output(seq_handle);
}

// ALSA MIDI リセット
void resetAlsaMidi(void) {
    if (!alsa_midi_initialized || !seq_handle) {
        return;
    }

    snd_seq_event_t ev;
    snd_seq_ev_clear(&ev);
    snd_seq_ev_set_source(&ev, seq_port);
    snd_seq_ev_set_dest(&ev, dest_client, dest_port);
    snd_seq_ev_set_direct(&ev);

    // 全チャンネルにリセット系のコントロールチェンジを送信
    for (int ch = 0; ch < 16; ch++) {
        // All Sound Off
        snd_seq_ev_set_controller(&ev, ch, 120, 0);
        snd_seq_event_output(seq_handle, &ev);

        // All Notes Off
        snd_seq_ev_set_controller(&ev, ch, 123, 0);
        snd_seq_event_output(seq_handle, &ev);

        // Reset All Controllers
        snd_seq_ev_set_controller(&ev, ch, 121, 0);
        snd_seq_event_output(seq_handle, &ev);
    }

    snd_seq_drain_output(seq_handle);
    fprintf(stderr, "ALSA MIDIがリセットされました\n");
}
